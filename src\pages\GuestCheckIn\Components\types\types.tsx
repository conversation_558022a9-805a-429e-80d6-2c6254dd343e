export interface GuestCheckItem {
  name: string;
  price: number;
  qty: number;
}

export interface GuestCheck {
  id: string;
  check_number: string;
  guest_count: number;
  opened_at: string;
  closed_at?: string;
  status: string;
  payment_status: string;
  sub_total: number;
  tax_total: number;
  service_charge_total: number;
  discount_total: number;
  grand_total: number;
  order: any;
  table_number: string;
  employee: string;
  linked_checks: any[];
}

export interface CheckInForm {
  tableNumber: string;
  guestCount: string;
  waiterName: string;
  specialRequests: string;
}