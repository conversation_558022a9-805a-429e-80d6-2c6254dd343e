import React, { useState } from "react";
import {
  Sidebar,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>barFooter,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { TeamSwitcher } from "@/app-components/sidebar/team-switcher";

import {
  Home,
  Cpu,
  ShoppingCart,
  Package,
  FileText,
  Users,
  MedalIcon,
  GitPullRequestArrow,
  BarChart2,
  Shield,
  Settings,
  Grid,
  UserCheck,
  Clock,
  Building2,
  Store,
  Monitor,
  Calculator,
  Printer,
  ClipboardList,
  Search,
  TrendingUp,
} from "lucide-react";
import { NavUser } from "./nav-user";
import { NavMain, MenuItem } from "./nav-main";
import { title } from "process";
import { Meta } from "react-router-dom";

const navData = {
  teams: [{ name: "GMC", logo: Cpu, plan: "Franchise" }],
  navMain: [{ title: "Dashboard", url: "/", icon: Home, isActive: true }],

  navPOS: [
    { title: "POS Dashboard", url: "/pos", icon: Grid },
    {
      title: "Branch Management",
      url: "/pos/branches",
      icon: Building2,
      items: [
        { title: "All Branches", url: "/pos/branches" },
        { title: "Add New Branch", url: "/pos/branches/new" },
      ],
    },
    {
      title: "Revenue Centers",
      url: "/pos/revenue-centers",
      icon: Store,
      items: [
        { title: "All Revenue Centers", url: "/pos/revenue-centers" },
        { title: "Add Revenue Center", url: "/pos/revenue-centers/new" },
      ],
    },
    {
      title: "Workstations",
      url: "/pos/workstations",
      icon: Monitor,
      items: [
        { title: "All Workstations", url: "/pos/workstations" },
        { title: "Add Workstation", url: "/pos/workstations/new" },
      ],
    },
    {
      title: "Tax Configuration",
      url: "/pos/tax-configuration",
      icon: Calculator,
    },
    {
      title: "Printer Management",
      url: "/pos/printers",
      icon: Printer,
      items: [
        { title: "All Printers", url: "/pos/printers" },
        { title: "Add Printer", url: "/pos/printers/new" },
      ],
    },
  ],
  navSetup: [
    {
      title: "Stores",
      url: "/stores",
      icon: Building2,
    },
    {
      title: "Cost Centers",
      url: "/cost-centers",
      icon: Store,
    },
    {
      title: "Units of Measure",
      url: "/units-of-measure",
      icon: Monitor,
    },
    {
      title: "Suppliers",
      url: "/suppliers",
      icon: Printer,
    },
    {
      title: "Product Categories",
      url: "/product-categories",
      icon: Printer,
    },
  ],
  navAdmin: [
    {
      title: "User Management",
      url: "/admin/users",
      icon: Users,
      items: [
        { title: "User List", url: "/admin/users" },
        { title: "User Groups", url: "/admin/users/groups" },
        { title: "User Permissions", url: "/admin/users/permissions" },
        { title: "Permissions Matrix", url: "/admin/users/permissions-matrix" },
        { title: "Groups Permissions", url: "/admin/users/groups-permissions" },
        { title: "Groups Matrix", url: "/admin/users/groups-matrix" },
      ],
    },
    {
      title: "Employee Management",
      url: "/admin/employees",
      icon: UserCheck,
      items: [
        { title: "Employee Profiles", url: "/admin/employees" },
        { title: "Shift Management", url: "/admin/shifts" },
      ],
    },

    { title: "System Settings", url: "/admin/settings", icon: Settings },
  ],
  navProcurement: [
    { title: "Procurement Dashboard", url: "/procurement", icon: Grid },
    {
      title: "Store Requisitions",
      url: "/procurement/store-requisitions",
      icon: ClipboardList,
    },
    {
      title: "Purchase Requisitions",
      url: "/procurement/purchase-requisitions",
      icon: FileText,
    },
    { title: "RFQs", url: "/procurement/rfqs", icon: Search },
    {
      title: "Purchase Orders",
      url: "/procurement/purchase-orders",
      icon: Package,
    },
    {
      title: "Bid Analysis",
      url: "/procurement/bid-analysis",
      icon: TrendingUp,
    },
  ],

  FoodMenu: [
    {
      title: "FoodMenu",
      url: "/Menu",
    },
  ] as MenuItem[],
  ComboMeal: [
    {
      title: "ComboMenu",
      url: "/Combo",
      icon: MedalIcon,
    },
  ],
  GuestCheck: [
    {
      title: "Guest Follow Up",
      url: "/CheckIn",
      icon: GitPullRequestArrow,
    },
    {
      title: "Check Tracker",
      url: "/CheckIn",
      icon: GitPullRequestArrow,
    },
  ] as MenuItem[],
  procurement: [
    {
      title: "Dashboard",
      url: "/procurement",
      icon: Home,
      isActive: false,
    },
    {
      title: "Store Requisitions",
      url: "/procurement/store-requisitions",
      icon: FileText,
      isActive: false,
    },
    {
      title: "Purchase Requisitions",
      url: "/procurement/purchase-requisitions",
      icon: ShoppingCart,
      isActive: false,
    },
    {
      title: "Purchase Orders",
      url: "/procurement/purchase-orders",
      icon: Package,
      isActive: false,
    },
  ] as MenuItem[],
  suppliers: [
    {
      title: "Suppliers",
      url: "/suppliers",
      icon: Users,
      isActive: false,
    },
  ] as MenuItem[],
};

export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  const [mainOpen, setMainOpen] = useState(true);
  const [posOpen, setPosOpen] = useState(false);
  const [adminOpen, setAdminOpen] = useState(false);
  const [setupOpen, setSetupOpen] = useState(false);
  // State to track which section is open
  const [openSection, setOpenSection] = useState<string | null>("Main");
  const [procurementOpen, setProcurementOpen] = useState(false);

  return (
    <Sidebar
      collapsible="icon"
      className="bg-sidebar text-sidebar-foreground"
      {...props}
    >
      <SidebarHeader className="border-b border-sidebar-border sticky top-0 z-10">
        <TeamSwitcher teams={navData.teams} />
      </SidebarHeader>

      <SidebarContent>
        <NavMain
          label="Main"
          items={navData.navMain}
          open={mainOpen}
          onToggle={() => setMainOpen(!mainOpen)}
        />

        <NavMain
          label="POS Configuration"
          items={navData.navPOS}
          open={posOpen}
          onToggle={() => setPosOpen(!posOpen)}
        />

        <NavMain
          label="Procurement"
          items={navData.navProcurement}
          open={procurementOpen}
          onToggle={() => setProcurementOpen(!procurementOpen)}
        />

        <NavMain
          label="Setups & Configations"
          items={navData.navSetup}
          open={setupOpen}
          onToggle={() => setSetupOpen(!setupOpen)}
        />

        <NavMain
          label="Administration"
          items={navData.navAdmin}
          open={adminOpen}
          onToggle={() => setAdminOpen(!adminOpen)}
        />

        <NavMain
          label="FoodMenu"
          items={navData.FoodMenu}
          open={openSection === "FoodMenu"}
          onToggle={() =>
            setOpenSection(openSection === "FoodMenu" ? null : "FoodMenu")
          }
        />

        <NavMain
          label="GuestCheck"
          items={navData.GuestCheck}
          open={openSection === "GuestCheck"}
          onToggle={() =>
            setOpenSection(openSection === "GuestCheck" ? null : "GuestCheck")
          }
        />

        <NavMain
          label="ComboMeal"
          items={navData.ComboMeal}
          open={openSection === "ComboMenu"}
          onToggle={() =>
            setOpenSection(openSection === "ComboMenu" ? null : "ComboMenu")
          }
        />
      </SidebarContent>

      {/* <SidebarFooter className="border-t border-gray-300 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-sidebar-foreground">
            Theme
          </span>
          <div className="flex items-center space-x-2">
            <Sun className={`w-4 h-4 transition-colors duration-300 ${
              theme === "light"
                ? "text-yellow-500"
                : "text-gray-400 dark:text-gray-500"
            }`} />
            <button
              onClick={toggleTheme}
              className="
                relative inline-flex items-center h-5 w-10
                rounded-full transition-all duration-300 ease-in-out
                bg-gray-200 dark:bg-gray-600
                hover:bg-gray-300 dark:hover:bg-gray-500
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
                focus:ring-offset-sidebar-background
                border border-gray-300 dark:border-gray-500
              "
              aria-label={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
              title={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
            >
              <span
                className={`${
                  theme === "dark" ? "translate-x-5" : "translate-x-0.5"
                } inline-block w-4 h-4 transform transition-transform duration-300 ease-in-out
                bg-white rounded-full shadow-md border border-gray-200 dark:border-gray-300`}
              />
            </button>
            <Moon className={`w-4 h-4 transition-colors duration-300 ${
              theme === "dark"
                ? "text-blue-400"
                : "text-gray-400"
            }`} />
          </div>
        </div>
      </SidebarFooter> */}
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
