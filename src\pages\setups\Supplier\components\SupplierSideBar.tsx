import { Button } from "@/components/ui/button";
import NameAvatar from "./NameAvatar";
import {
  AtSign,
  Edit,
  Mail,
  MapPin,
  MessageSquare,
  Pencil,
  Phone,
  UserCheck2,
  UserMinus2,
  UserRoundXIcon,
} from "lucide-react";
import { supplierType } from "@/types/suppliers";
import copyToClipboard from "@/utils/copyToClipboard";
import { Badge } from "@/components/ui/badge";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { formatDate } from "@/utils/formatDate";

type Props = {
  supplier: supplierType;
};

const SupplierSideBar = ({ supplier }: Props) => {
  return (
    <div>
      <div className="">
        {/* Avatar and Basic Info - Horizontal Layout */}
        <div className="flex items-start mb-6">
          <div className="relative mr-4">
            <NameAvatar name={supplier?.name} size="lg" />
            <Button
              variant="outline"
              size="icon"
              // onClick={onEdit}
              className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-white shadow-md border-2"
            >
              <Pencil className="h-3 w-3" />
              <span className="sr-only">Edit</span>
            </Button>
          </div>
          <div>
            <h2 className="text-lg font-semibold">{supplier?.name || "N/A"}</h2>
            <p className="text-sm text-muted-foreground">
              Code: {supplier?.code || "N/A"}
            </p>
            <p className="text-sm text-muted-foreground">
              {supplier?.phone_number || "N/A"}
            </p>
          </div>
        </div>

        {/* Quick Action Buttons - Now Direct Links */}
        <div className="grid grid-cols-4 gap-2 mb-6 border-t border-b py-3">
          {supplier?.email && (
            <a href={`mailto:${supplier?.email}`} className="block">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center justify-center w-full text-primary dark:text-primary-light"
              >
                <Mail className="h-4 w-4 mr-2" />
                {/* <span>Email</span> */}
              </Button>
            </a>
          )}
          {!supplier?.email && (
            <Button
              disabled
              variant="outline"
              size="sm"
              className="flex items-center justify-center w-full"
              title="Email"
            >
              <Mail className="h-4 w-4 mr-2" />
              {/* <span>Email</span> */}
            </Button>
          )}

          <a
            href="https://web.whatsapp.com"
            target="_blank"
            rel="noopener noreferrer"
            className="block"
          >
            <Button
              variant="outline"
              size="sm"
              title="WhatsApp"
              className="flex items-center justify-center w-full text-primary dark:text-primary-light"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 32 32"
                fill="currentColor"
              >
                <path d="M16.004 3.003c-7.18 0-13 5.82-13 13 0 2.284.598 4.524 1.73 6.496L3 29l6.68-1.74a12.902 12.902 0 0 0 6.324 1.603c7.18 0 13-5.82 13-13s-5.82-12.86-13-12.86zm0 23.573c-2.027 0-4.015-.547-5.756-1.582l-.41-.242-3.968 1.034 1.058-3.866-.266-.412a10.451 10.451 0 0 1-1.61-5.642c0-5.785 4.712-10.497 10.497-10.497s10.497 4.712 10.497 10.497-4.712 10.497-10.497 10.497zm5.76-7.91c-.316-.158-1.868-.922-2.158-1.027-.29-.106-.502-.158-.715.158-.211.316-.82 1.026-1.005 1.242-.184.211-.368.237-.684.079-.316-.158-1.336-.492-2.547-1.566-.94-.84-1.574-1.875-1.757-2.191-.184-.316-.02-.487.138-.645.143-.144.316-.368.474-.553.158-.184.211-.316.316-.527.106-.211.053-.395-.026-.553-.08-.158-.715-1.729-.98-2.364-.257-.618-.519-.533-.715-.543-.184-.01-.395-.01-.605-.01-.211 0-.553.08-.843.395s-1.106 1.08-1.106 2.633 1.133 3.054 1.29 3.267c.158.211 2.228 3.4 5.398 4.768.755.326 1.342.521 1.801.667.757.237 1.447.204 1.99.124.606-.09 1.868-.763 2.132-1.498.263-.737.263-1.368.184-1.498-.079-.132-.29-.21-.605-.369z" />
              </svg>

              {/* <span>WhatsApp</span> */}
            </Button>
          </a>

          {supplier?.phone_number && (
            <a href={`tel:${supplier?.phone_number}`} className="block">
              <Button
                variant="outline"
                size="sm"
                title="Call"
                className="flex items-center justify-center w-full text-primary dark:text-primary-light"
              >
                <Phone className="h-4 w-4 mr-2" />
                {/* <span>Call</span> */}
              </Button>
            </a>
          )}
          {!supplier?.phone_number && (
            <Button
              disabled
              variant="outline"
              size="sm"
              title="Call"
              className="flex items-center justify-center w-full"
            >
              <Phone className="h-4 w-4 mr-2" />
              {/* <span>Call</span> */}
            </Button>
          )}

          <a
            href="https://m.me"
            target="_blank"
            rel="noopener noreferrer"
            className="block"
          >
            <Button
              variant="outline"
              size="sm"
              title="Message"
              className="flex items-center justify-center w-full text-primary dark:text-primary-light"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              {/* <span>Message</span> */}
            </Button>
          </a>
        </div>

        <div className="space-y-6">
          {/* Contact Information */}
          <div className="space-y-3">
            <h3 className="font-bold text-sm">Contact Information</h3>
            <div className="grid grid-cols-1 gap-2">
              {supplier?.phone_number && (
                <div className="flex items-center group">
                  <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span>{supplier?.phone_number}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() =>
                      copyToClipboard(supplier?.phone_number || "")
                    }
                  >
                    <svg className="h-3 w-3" viewBox="0 0 24 24">
                      <rect
                        x="9"
                        y="9"
                        width="13"
                        height="13"
                        rx="2"
                        ry="2"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      />
                      <path
                        d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      />
                    </svg>
                  </Button>
                </div>
              )}
              {supplier?.alt_phone_number && (
                <div className="flex items-center group">
                  <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span>{supplier?.alt_phone_number}</span>
                  <Badge
                    variant="outline"
                    className="ml-2 text-xs text-primary dark:text-primary-light border-primary"
                  >
                    Alt
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() =>
                      copyToClipboard(supplier?.alt_phone_number || "")
                    }
                  >
                    <svg className="h-3 w-3" viewBox="0 0 24 24">
                      <rect
                        x="9"
                        y="9"
                        width="13"
                        height="13"
                        rx="2"
                        ry="2"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      />
                      <path
                        d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      />
                    </svg>
                  </Button>
                </div>
              )}
              {supplier?.email && (
                <div className="flex items-center group">
                  <AtSign className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span>{supplier?.email}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => copyToClipboard(supplier?.email || "")}
                  >
                    <svg className="h-3 w-3" viewBox="0 0 24 24">
                      <rect
                        x="9"
                        y="9"
                        width="13"
                        height="13"
                        rx="2"
                        ry="2"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      />
                      <path
                        d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      />
                    </svg>
                  </Button>
                </div>
              )}

              {supplier?.country && (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span className="truncate">{`${supplier?.city_or_state}, ${supplier?.country}`}</span>
                </div>
              )}
            </div>
          </div>

          {/* actions  */}
          <div className="border-y py-3 my-4">
            <h3 className="font-bold mb-2 text-sm">supplier Action</h3>
            <div className="flex space-x-2 justify-stretch w-full">
              <PrimaryButton
                variant="outline"
                size="sm"
                // onClick={() => handleViewsupplier()}
              >
                <span title="Edit" className="flex items-center gap-2">
                  <Edit />
                </span>
              </PrimaryButton>
              <PrimaryButton
                variant="outline"
                size="sm"
                // onClick={() => handleViewsupplier()}
              >
                <span title="Blacklist" className="flex items-center gap-2">
                  <UserMinus2 />
                </span>
              </PrimaryButton>

              {supplier.status == "Active" ? (
                <PrimaryButton
                  variant="outline"
                  size="sm"
                  //   onClick={() => handleReallocatesupplier("Allocate")}
                >
                  <span className="flex items-center gap-2" title="Deactivate">
                    <UserRoundXIcon />
                  </span>
                </PrimaryButton>
              ) : (
                <PrimaryButton
                  variant="outline"
                  size="sm"
                  //   onClick={() => handleReallocatesupplier("Reallocate")}
                >
                  <span className="flex items-center gap-2" title="Activate">
                    <UserCheck2 /> Activate
                  </span>
                </PrimaryButton>
              )}
            </div>
          </div>

          {/* Additional Details */}
          <div className="space-y-3">
            <h3 className="font-bold text-sm">Additional Details</h3>
            <div className="grid grid-cols-2 gap-x-4 gap-y-3 text-sm">
              <div className="col-span-2">
                <p className="text-xs text-muted-foreground">Branch</p>
                <p>{supplier?.branch || "N/A"}</p>
              </div>
              <div>
                <p className="col-span-2 text-xs text-muted-foreground">
                  Address
                </p>
                <p>
                  {supplier?.address_mailing} <br />{" "}
                  {supplier?.address_physical} {supplier?.city_or_state}{" "}
                  {supplier?.country || "N/A"}
                </p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">Category</p>
                <p>{supplier?.category || "N/A"}</p>
              </div>
            </div>
          </div>

          {/* Bank Information */}
          <div className="space-y-3">
            <h3 className="font-bold text-sm">Payment Information</h3>
            <div className="grid grid-cols-2 gap-x-4 gap-y-3 text-sm">
              <div>
                <p className="text-xs text-muted-foreground">Pay Terms</p>
                <p>{supplier?.payment_terms}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">GL Account</p>
                <p>{formatDate(supplier?.gl_account)}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">Has VAT?</p>
                <p>{supplier?.tax_vat_16 || "N/A"}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">
                  Is Tax Exempted?
                </p>
                <p>{supplier?.tax_exempt || "N/A"}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierSideBar;
