import { apiSlice } from "../apiSlice";

export const costCenterApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCostCenters: builder.query({
      query: (params) => ({
        url: "/costCenters",
        method: "GET",
        params: params,
      }),
    }),

    retrieveCostCenter: builder.query({
      query: (id) => ({
        url: `/costCenters/${id}`,
        method: "GET",
      }),
    }),

    addCostCenters: builder.mutation({
      query: (payload) => ({
        url: "/costCenters",
        method: "POST",
        body: payload,
      }),
    }),

    patchCostCenters: builder.mutation({
      query: (payload) => ({
        url: `/costCenters/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
  useGetCostCentersQuery,
  useRetrieveCostCenterQuery,
  useAddCostCentersMutation,
  usePatchCostCentersMutation,

  useLazyGetCostCentersQuery,
  useLazyRetrieveCostCenterQuery,
} = costCenterApiSlice;
