import { Screen } from "@/app-components/layout/screen";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import AddCostCenter from "./modals/AddCostCenter";
import { DataTable } from "@/components/custom/tables/Table1";
import { costCenterType } from "@/types/costCenter";
import { costCenterTestData } from "./costTestData";
import { ColumnDef } from "@tanstack/react-table";
import { searchDebouncer } from "@/utils/debouncers";
import { supplierTestData } from "../Supplier/supplierTestData";
import { Link } from "react-router-dom";
import { useGetCostCentersQuery } from "@/redux/slices/costCenter";
import CostCenterDetails from "./modals/CostCenterDetails";

const index = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCostCenter, setSelectedCostCenter] =
    useState<costCenterType | null>(null);

  const handleViewDetails = (costCenter: costCenterType) => {
    setSelectedCostCenter(costCenter);
    setIsDetailModalOpen(true);
  };

  const handleEdit = (costCenter: costCenterType) => {
    setSelectedCostCenter(costCenter);
    setIsEditModalOpen(true);
  };

  //   const {
  //     data: costCenters,
  //     isLoading: loading,
  //     isFetching,
  //     isError,
  //     error,
  //   } = useGetCostCentersQuery({
  //     page: currentPage,
  //     page_size: itemsPerPage,
  //     search: searchValue,
  //     status: status,
  //   });

  const columns: ColumnDef<costCenterType>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => info.getValue(),

      //   cell: (info) => (
      //     <Link
      //       to={`/cost-centers/${info?.row?.original?.id}`}
      //       title="View Supplier"
      //     >
      //       <span className="font-medium underline capitalize text-blue-400">
      //         {info.getValue() as string}
      //       </span>
      //     </Link>
      //   ),

      enableColumnFilter: false,
    },
    {
      accessorKey: "code",
      header: "Code",
      cell: (info) => info.getValue(),

      enableColumnFilter: false,
    },
    {
      accessorKey: "gl_account",
      header: "GL Account",
      cell: (info) => info.getValue(),

      enableColumnFilter: false,
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: (info) => info.getValue(),

      enableColumnFilter: false,
    },
    {
      accessorKey: "default_store",
      header: "Default Store",
      cell: (info) => info.getValue(),

      enableColumnFilter: false,
    },
    {
      accessorKey: "budget_limit",
      header: "Budget Limit",
      cell: (info) => info.getValue(),

      enableColumnFilter: false,
    },
    {
      accessorKey: "budget_frequency",
      header: "Budget Frequency",
      cell: (info) => info.getValue(),

      enableColumnFilter: false,
    },
    {
      accessorKey: "is_active",
      header: "Status",
      cell: (info) => (
        <span
          className={`${
            info.getValue()
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          } px-2 py-1 rounded-full text-xs font-medium`}
        >
          {info.getValue() ? "Active" : "Inactive"}
        </span>
      ),

      enableColumnFilter: false,
    },
    {
      accessorKey: "actions",
      header: "Actions",
      cell: (info) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewDetails(info.row.original)}
          >
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(info.row.original)}
          >
            Edit
          </Button>
          {/* <Button
            variant="outline"
            size="sm"
            onClick={() => handleDelete(info.row.original)}
          >
            Delete
          </Button> */}
        </div>
      ),
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold">Cost Centers</h1>
        <div className="flex items-center gap-2">
          {/* <RefreshPermissionsButton /> */}
          <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
            Add Cost Center
          </Button>
        </div>
      </div>

      <DataTable<costCenterType>
        // data={suppliers?.data?.results || []}
        data={costCenterTestData || []}
        columns={columns}
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enableColumnFilters={true}
        enableSorting={true}
        enablePrintPdf={true}
        tableClassName="border-collapse"
        tHeadClassName="bg-accent"
        tHeadCellsClassName="text-xs uppercase text-gray-200 font-semibold"
        tBodyTrClassName="hover:bg-gray-50"
        tBodyCellsClassName="border-t"
        searchInput={
          <input
            value={searchInput}
            name="searchInput"
            type="search"
            onChange={(e) =>
              searchDebouncer(e.target.value, setSearchInput, setSearchValue)
            }
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search cost centers..."
          />
        }
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        // totalItems={suppliers?.data?.total_data || 0}
        totalItems={costCenterTestData.length || 0}
      />

      {isAddModalOpen && (
        <AddCostCenter
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}

      {isDetailModalOpen && (
        <CostCenterDetails
          isOpen={isDetailModalOpen}
          onClose={() => setIsDetailModalOpen(false)}
          costCenter={selectedCostCenter!}
        />
      )}

      {isEditModalOpen && (
        <AddCostCenter
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          updateData={selectedCostCenter!}
        />
      )}
    </Screen>
  );
};

export default index;
