import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  User,
  Users,
  Building,
  Clock,
  Calendar,
  Eye,
  UserCheck,
  UserX,
  MapPin,
  Phone,
  Mail,
  IdCard,
  DollarSign,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  <PERSON>alogD<PERSON>cription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Screen } from "@/app-components/layout/screen";

// Mock data for employees
const mockEmployees = [
  {
    id: 1,
    employeeNumber: "EMP001",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+254 712 345 678",
    nationalId: "12345678",
    dateOfBirth: "1990-05-15",
    role: "Waiter",
    branch: "Nairobi Branch",
    revenueCenter: "Restaurant",
    hireDate: "2023-01-15",
    employmentType: "Permanent",
    hireStatus: "Active",
    isSalaried: true,
    workPermitExpiry: "2025-01-15",
    lastLogin: "2024-01-15 10:30 AM",
    avatar: "/api/placeholder/50/50",
  },
  {
    id: 2,
    employeeNumber: "EMP002",
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>",
    phone: "+254 723 456 789",
    nationalId: "87654321",
    dateOfBirth: "1988-08-22",
    role: "Chef",
    branch: "Mombasa Branch",
    revenueCenter: "Kitchen",
    hireDate: "2022-06-10",
    employmentType: "Permanent",
    hireStatus: "Active",
    isSalaried: true,
    workPermitExpiry: "2024-06-10",
    lastLogin: "2024-01-15 09:15 AM",
    avatar: "/api/placeholder/50/50",
  },
  {
    id: 3,
    employeeNumber: "EMP003",
    firstName: "Mike",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "+254 734 567 890",
    nationalId: "11223344",
    dateOfBirth: "1995-03-10",
    role: "Cashier",
    branch: "Kisumu Branch",
    revenueCenter: "Front Desk",
    hireDate: "2023-09-01",
    employmentType: "Part-time",
    hireStatus: "Leave",
    isSalaried: false,
    workPermitExpiry: "2025-09-01",
    lastLogin: "2024-01-10 04:20 PM",
    avatar: "/api/placeholder/50/50",
  },
  {
    id: 4,
    employeeNumber: "EMP004",
    firstName: "Sarah",
    lastName: "Wilson",
    email: "<EMAIL>",
    phone: "+254 745 678 901",
    nationalId: "99887766",
    dateOfBirth: "1992-12-05",
    role: "Manager",
    branch: "Nairobi Branch",
    revenueCenter: "Administration",
    hireDate: "2021-03-20",
    employmentType: "Permanent",
    hireStatus: "Terminated",
    isSalaried: true,
    workPermitExpiry: "2026-03-20",
    lastLogin: "2024-01-05 02:10 PM",
    avatar: "/api/placeholder/50/50",
  },
];

const roles = ["Waiter", "Chef", "Cashier", "Manager", "Supervisor", "Cleaner"];
const branches = ["Nairobi Branch", "Mombasa Branch", "Kisumu Branch", "Nakuru Branch"];
const revenueCenters = ["Restaurant", "Kitchen", "Front Desk", "Administration", "Bar"];
const employmentTypes = ["Permanent", "Part-time", "Temporary", "Contract"];
const hireStatuses = ["Active", "Terminated", "Leave", "Probation"];

export default function EmployeeProfile() {
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [branchFilter, setBranchFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newEmployee, setNewEmployee] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    nationalId: "",
    dateOfBirth: "",
    role: "",
    branch: "",
    revenueCenter: "",
    employmentType: "",
    hireDate: "",
    isSalaried: false,
    workPermitExpiry: "",
  });
  const navigate = useNavigate();

  const filteredEmployees = mockEmployees.filter((employee) => {
    const matchesSearch = employee.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.employeeNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === "all" || employee.role === roleFilter;
    const matchesBranch = branchFilter === "all" || employee.branch === branchFilter;
    const matchesStatus = statusFilter === "all" || employee.hireStatus === statusFilter;
    return matchesSearch && matchesRole && matchesBranch && matchesStatus;
  });

  const handleCreateEmployee = () => {
    // Handle employee creation logic here
    console.log("Creating employee:", newEmployee);
    setIsCreateModalOpen(false);
    setNewEmployee({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      nationalId: "",
      dateOfBirth: "",
      role: "",
      branch: "",
      revenueCenter: "",
      employmentType: "",
      hireDate: "",
      isSalaried: false,
      workPermitExpiry: "",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Terminated":
        return "bg-red-100 text-red-800";
      case "Leave":
        return "bg-yellow-100 text-yellow-800";
      case "Probation":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const activeEmployees = mockEmployees.filter(emp => emp.hireStatus === "Active").length;
  const terminatedEmployees = mockEmployees.filter(emp => emp.hireStatus === "Terminated").length;
  const onLeaveEmployees = mockEmployees.filter(emp => emp.hireStatus === "Leave").length;

  return (
    <Screen>
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/admin/users">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Users
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Employee Profiles</h1>
            <p className="text-muted-foreground">
              Manage staff profiles, work permits, and employment details
            </p>
          </div>
        </div>
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Employee
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Employee</DialogTitle>
              <DialogDescription>
                Create a new employee profile with all necessary details.
              </DialogDescription>
            </DialogHeader>
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="work">Work Details</TabsTrigger>
                <TabsTrigger value="employment">Employment</TabsTrigger>
              </TabsList>
              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={newEmployee.firstName}
                      onChange={(e) => setNewEmployee(prev => ({ ...prev, firstName: e.target.value }))}
                      placeholder="Enter first name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={newEmployee.lastName}
                      onChange={(e) => setNewEmployee(prev => ({ ...prev, lastName: e.target.value }))}
                      placeholder="Enter last name"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newEmployee.email}
                    onChange={(e) => setNewEmployee(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="Enter email address"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={newEmployee.phone}
                      onChange={(e) => setNewEmployee(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="Enter phone number"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nationalId">National ID</Label>
                    <Input
                      id="nationalId"
                      value={newEmployee.nationalId}
                      onChange={(e) => setNewEmployee(prev => ({ ...prev, nationalId: e.target.value }))}
                      placeholder="Enter national ID"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dateOfBirth">Date of Birth</Label>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={newEmployee.dateOfBirth}
                    onChange={(e) => setNewEmployee(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                  />
                </div>
              </TabsContent>
              <TabsContent value="work" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="role">Employee Role *</Label>
                  <Select
                    value={newEmployee.role}
                    onValueChange={(value) => setNewEmployee(prev => ({ ...prev, role: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map((role) => (
                        <SelectItem key={role} value={role}>
                          {role}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="branch">Branch *</Label>
                    <Select
                      value={newEmployee.branch}
                      onValueChange={(value) => setNewEmployee(prev => ({ ...prev, branch: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select branch" />
                      </SelectTrigger>
                      <SelectContent>
                        {branches.map((branch) => (
                          <SelectItem key={branch} value={branch}>
                            {branch}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="revenueCenter">Revenue Center *</Label>
                    <Select
                      value={newEmployee.revenueCenter}
                      onValueChange={(value) => setNewEmployee(prev => ({ ...prev, revenueCenter: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select revenue center" />
                      </SelectTrigger>
                      <SelectContent>
                        {revenueCenters.map((center) => (
                          <SelectItem key={center} value={center}>
                            {center}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="workPermitExpiry">Work Permit Expiry</Label>
                  <Input
                    id="workPermitExpiry"
                    type="date"
                    value={newEmployee.workPermitExpiry}
                    onChange={(e) => setNewEmployee(prev => ({ ...prev, workPermitExpiry: e.target.value }))}
                  />
                </div>
              </TabsContent>
              <TabsContent value="employment" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="hireDate">Hire Date *</Label>
                  <Input
                    id="hireDate"
                    type="date"
                    value={newEmployee.hireDate}
                    onChange={(e) => setNewEmployee(prev => ({ ...prev, hireDate: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employmentType">Employment Type *</Label>
                  <Select
                    value={newEmployee.employmentType}
                    onValueChange={(value) => setNewEmployee(prev => ({ ...prev, employmentType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select employment type" />
                    </SelectTrigger>
                    <SelectContent>
                      {employmentTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isSalaried"
                    checked={newEmployee.isSalaried}
                    onCheckedChange={(checked) => setNewEmployee(prev => ({ ...prev, isSalaried: checked as boolean }))}
                  />
                  <Label htmlFor="isSalaried">Is Salaried?</Label>
                </div>
              </TabsContent>
            </Tabs>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateEmployee}>Create Employee</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockEmployees.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeEmployees}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On Leave</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{onLeaveEmployees}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Terminated</CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{terminatedEmployees}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search employees..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All Roles" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            {roles.map((role) => (
              <SelectItem key={role} value={role}>
                {role}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={branchFilter} onValueChange={setBranchFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All Branches" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Branches</SelectItem>
            {branches.map((branch) => (
              <SelectItem key={branch} value={branch}>
                {branch}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All Statuses" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            {hireStatuses.map((status) => (
              <SelectItem key={status} value={status}>
                {status}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Employees Table */}
      <Card>
        <CardHeader>
          <CardTitle>Employee List</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Branch</TableHead>
                <TableHead>Employment</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEmployees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={employee.avatar} />
                        <AvatarFallback>
                          {employee.firstName[0]}{employee.lastName[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">
                          {employee.firstName} {employee.lastName}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {employee.employeeNumber}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {employee.email}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{employee.role}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{employee.branch}</div>
                      <div className="text-sm text-muted-foreground">
                        {employee.revenueCenter}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{employee.employmentType}</div>
                      <div className="text-sm text-muted-foreground">
                        {employee.isSalaried ? "Salaried" : "Hourly"}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(employee.hireStatus)}>
                      {employee.hireStatus}
                    </Badge>
                  </TableCell>
                  <TableCell>{employee.lastLogin}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <User className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem
                          onClick={() => navigate(`/admin/employees/${employee.id}`)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Profile
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => navigate(`/admin/employees/${employee.id}/edit`)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Employee
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => navigate(`/admin/employees/${employee.id}/shifts`)}
                        >
                          <Clock className="mr-2 h-4 w-4" />
                          Manage Shifts
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {employee.hireStatus === "Active" ? (
                          <DropdownMenuItem className="text-red-600">
                            <UserX className="mr-2 h-4 w-4" />
                            Deactivate
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem className="text-green-600">
                            <UserCheck className="mr-2 h-4 w-4" />
                            Activate
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Employee
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
    </Screen>
  );
}