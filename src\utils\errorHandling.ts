import { toast } from 'sonner';

/**
 * Extracts a user-friendly error message from various error formats
 * @param error - The error object from API calls
 * @param defaultMessage - Default message to show if no specific error is found
 * @returns A user-friendly error message
 */
export const extractErrorMessage = (error: any, defaultMessage: string = 'An error occurred'): string => {
  // Handle RTK Query errors
  if (error?.data) {
    if (typeof error.data === 'string') {
      return error.data;
    }
    
    if (error.data.message) {
      return error.data.message;
    }
    
    if (error.data.detail) {
      return error.data.detail;
    }
    
    if (error.data.error) {
      return error.data.error;
    }
    
    // Handle validation errors
    if (error.data.errors && Array.isArray(error.data.errors)) {
      return error.data.errors.join(', ');
    }
    
    // Handle field-specific errors
    if (typeof error.data === 'object') {
      const firstError = Object.values(error.data)[0];
      if (Array.isArray(firstError)) {
        return firstError[0];
      }
      if (typeof firstError === 'string') {
        return firstError;
      }
    }
  }
  
  // Handle standard Error objects
  if (error?.message) {
    return error.message;
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    return error;
  }
  
  return defaultMessage;
};

/**
 * Shows a success toast notification
 * @param message - Success message to display
 */
export const showSuccessToast = (message: string) => {
  toast.success(message);
};

/**
 * Shows an error toast notification with extracted error message
 * @param error - The error object
 * @param defaultMessage - Default message if error extraction fails
 */
export const showErrorToast = (error: any, defaultMessage: string = 'An error occurred') => {
  const message = extractErrorMessage(error, defaultMessage);
  toast.error(message);
};

/**
 * Shows an info toast notification
 * @param message - Info message to display
 */
export const showInfoToast = (message: string) => {
  toast.info(message);
};

/**
 * Shows a warning toast notification
 * @param message - Warning message to display
 */
export const showWarningToast = (message: string) => {
  toast.warning(message);
};

/**
 * Generic API error handler for React Query/RTK Query
 * @param error - The error from the API call
 * @param operation - Description of the operation that failed
 */
export const handleApiError = (error: any, operation: string = 'operation') => {
  console.error(`Error during ${operation}:`, error);
  showErrorToast(error, `Failed to ${operation}. Please try again.`);
};

/**
 * Generic API success handler
 * @param message - Success message to display
 * @param data - Optional data from the successful operation
 */
export const handleApiSuccess = (message: string, data?: any) => {
  if (data) {
    console.log('Operation successful:', data);
  }
  showSuccessToast(message);
};
