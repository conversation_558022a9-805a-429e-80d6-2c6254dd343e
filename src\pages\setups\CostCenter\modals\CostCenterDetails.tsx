import { Edit } from "lucide-react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { costCenterType } from "@/types/costCenter";
import { Button } from "@/components/ui/button";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  costCenter?: costCenterType;
}

const CostCenterDetails = ({ isOpen, onClose, costCenter }: propTypes) => {
  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Cost Center Details"
      description="Enter cost center details"
    >
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm text-gray-500">Name</h3>
            <p className="text-lg font-medium">{costCenter?.name}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Code</h3>
            <p className="text-lg font-medium">{costCenter?.code}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">GL Account</h3>
            <p className="text-lg font-medium">{costCenter?.gl_account}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Default Store</h3>
            <p className="text-lg font-medium">{costCenter?.default_store}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Budget Limit</h3>
            <p className="text-lg font-medium">{costCenter?.budget_limit}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Budget Frequency</h3>
            <p className="text-lg font-medium">
              {costCenter?.budget_frequency}
            </p>
          </div>
          <div className="col-span-2">
            <h3 className="text-sm text-gray-500">Description</h3>
            <p className="text-lg font-medium">{costCenter?.description}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Status</h3>
            <p
              className={`text-lg font-medium ${
                costCenter?.is_active ? "text-green-500" : "text-red-500"
              }`}
            >
              {costCenter?.is_active ? "Active" : "Inactive"}
            </p>
          </div>
        </div>
        <div className="w-full flex justify-end gap-2 mt-6 border-t pt-4">
          <Button type="button" variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button type="button" variant="default">
            <Edit /> Edit
          </Button>
        </div>
      </div>
    </BaseModal>
  );
};

export default CostCenterDetails;
