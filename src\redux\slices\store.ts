import { apiSlice } from "../apiSlice";

export const StoreApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getStores: builder.query({
      query: (params) => ({
        url: "/stores",
        method: "GET",
        params: params,
      }),
    }),

    retrieveStore: builder.query({
      query: (id) => ({
        url: `/stores/${id}`,
        method: "GET",
      }),
    }),

    addStores: builder.mutation({
      query: (payload) => ({
        url: "/stores",
        method: "POST",
        body: payload,
      }),
    }),

    patchStores: builder.mutation({
      query: (payload) => ({
        url: `/stores/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
  useGetStoresQuery,
  useRetrieveStoreQuery,
  useAddStoresMutation,
  usePatchStoresMutation,

  useLazyGetStoresQuery,
  useLazyRetrieveStoreQuery,
} = StoreApiSlice;
