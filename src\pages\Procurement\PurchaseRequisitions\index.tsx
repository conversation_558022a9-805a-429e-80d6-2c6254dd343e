import { Screen } from "@/app-components/layout/screen";
import { useState } from "react";
import {
  useGetPurchaseRequisitionsQuery,
  useGetDepartmentsQuery,
  useGetSupplierCategoriesQuery,
  useSubmitPurchaseRequisitionMutation,
  useApprovePurchaseRequisitionMutation,
  useRejectPurchaseRequisitionMutation,
  useConvertToRFQMutation
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { PurchaseRequisition } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { searchDebouncer } from "@/utils/debouncers";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MoreHorizontal, Eye, Edit, Send, CheckCircle, XCircle, FileText, Users, Plus } from "lucide-react";
import AddPurchaseRequisition from "./modals/AddPurchaseRequisition";
import { toast } from "@/components/custom/Toast/MyToast";

const PurchaseRequisitionsIndex = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [statusFilter, setStatusFilter] = useState("");
  const [departmentFilter, setDepartmentFilter] = useState("all");
  const [supplierCategoryFilter, setSupplierCategoryFilter] = useState("all");

  // Fetch data
  const {
    data: purchaseRequisitions,
  } = useGetPurchaseRequisitionsQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
    status: statusFilter,
    department: departmentFilter === "all" ? "" : departmentFilter,
    supplier_category: supplierCategoryFilter === "all" ? "" : supplierCategoryFilter,
  });

  const { data: departments } = useGetDepartmentsQuery({});
  const { data: supplierCategories } = useGetSupplierCategoriesQuery({});

  // Mutation hooks
  const [submitPurchaseRequisition] = useSubmitPurchaseRequisitionMutation();
  const [approvePurchaseRequisition] = useApprovePurchaseRequisitionMutation();
  const [rejectPurchaseRequisition] = useRejectPurchaseRequisitionMutation();
  const [convertToRFQ] = useConvertToRFQMutation();

  // Handler functions
  const handleSubmit = async (id: number) => {
    try {
      await submitPurchaseRequisition(id).unwrap();
      toast.success("Purchase requisition submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit requisition");
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await approvePurchaseRequisition(id).unwrap();
      toast.success("Purchase requisition approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve requisition");
    }
  };

  const handleReject = async (id: number) => {
    const reason = prompt("Please provide a reason for rejection:");
    if (!reason) return;

    try {
      await rejectPurchaseRequisition({ id, reason }).unwrap();
      toast.success("Purchase requisition rejected");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to reject requisition");
    }
  };

  const handleConvertToRFQ = async (id: number) => {
    try {
      await convertToRFQ(id).unwrap();
      toast.success("Successfully converted to RFQ");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to convert to RFQ");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
      Submitted: { variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      Approved: { variant: "default" as const, color: "bg-green-100 text-green-800" },
      Rejected: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
      "Converted to RFQ": { variant: "default" as const, color: "bg-purple-100 text-purple-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;

    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      Low: { color: "bg-gray-100 text-gray-800" },
      Medium: { color: "bg-yellow-100 text-yellow-800" },
      High: { color: "bg-orange-100 text-orange-800" },
      Urgent: { color: "bg-red-100 text-red-800" },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.Low;

    return (
      <Badge className={config.color}>
        {priority}
      </Badge>
    );
  };

  const columns: ColumnDef<PurchaseRequisition>[] = [
    {
      accessorKey: "pr_number",
      header: "PR Number",
      cell: (info) => (
        <Link
          to={`/procurement/purchase-requisitions/${info?.row?.original?.id}`}
          title="View Purchase Requisition"
        >
          <span className="font-medium underline capitalize text-blue-400">
            {info.getValue() as string || `PR-${String(info?.row?.original?.id).padStart(4, '0')}`}
          </span>
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "store_requisition_number",
      header: "Source SR",
      cell: (info) => {
        const value = info.getValue() as string;
        return value ? (
          <Link
            to={`/procurement/store-requisitions/${info?.row?.original?.store_requisition}`}
            className="text-blue-600 hover:underline"
          >
            {value}
          </Link>
        ) : "N/A";
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "department",
      header: "Department",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "supplier_category",
      header: "Category",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "assigned_to_name",
      header: "Assigned To",
      cell: (info) => {
        const value = info.getValue() as string;
        return value ? (
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-gray-500" />
            {value}
          </div>
        ) : (
          <span className="text-gray-400">Unassigned</span>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: (info) => {
        const priority = info.getValue() as string;
        return priority ? getPriorityBadge(priority) : <span className="text-gray-400">-</span>;
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "required_date",
      header: "Required By",
      cell: (info) => {
        const date = info.getValue() as string;
        return date ? new Date(date).toLocaleDateString() : "N/A";
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => getStatusBadge(info.getValue() as string),
      enableColumnFilter: false,
    },
    {
      accessorKey: "total_estimated_cost",
      header: "Est. Cost",
      cell: (info) => {
        const cost = info.getValue() as number;
        const currency = info.row.original.currency || "KES";
        return cost ? `${currency} ${cost.toLocaleString()}` : "N/A";
      },
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const requisition = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to={`/procurement/purchase-requisitions/${requisition.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              {requisition.status === "Draft" && (
                <>
                  <DropdownMenuItem>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSubmit(requisition.id!)}>
                    <Send className="mr-2 h-4 w-4" />
                    Submit
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Submitted" && (
                <>
                  <DropdownMenuItem onClick={() => handleApprove(requisition.id!)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleReject(requisition.id!)}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Approved" && (
                <DropdownMenuItem onClick={() => handleConvertToRFQ(requisition.id!)}>
                  <FileText className="mr-2 h-4 w-4" />
                  Convert to RFQ
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Purchase Requisitions</h1>
            <p className="text-gray-600 mt-1">Manage purchase requests and approvals</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Purchase Requisition
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 items-center">
          {/* Status Filter */}
          <div className="flex gap-2">
            <Button
              variant={statusFilter === "" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("")}
            >
              All Status
            </Button>
            <Button
              variant={statusFilter === "Draft" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Draft")}
            >
              Draft
            </Button>
            <Button
              variant={statusFilter === "Submitted" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Submitted")}
            >
              Submitted
            </Button>
            <Button
              variant={statusFilter === "Approved" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Approved")}
            >
              Approved
            </Button>
            <Button
              variant={statusFilter === "Converted to RFQ" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Converted to RFQ")}
            >
              Converted to RFQ
            </Button>
          </div>

          {/* Department Filter */}
          <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by Department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments?.data?.results?.map((dept: any) => (
                <SelectItem key={dept.id} value={dept.name}>
                  {dept.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Supplier Category Filter */}
          <Select value={supplierCategoryFilter} onValueChange={setSupplierCategoryFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {supplierCategories?.data?.results?.map((category: any) => (
                <SelectItem key={category.id} value={category.name}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Data Table */}
        <DataTable<PurchaseRequisition>
          data={purchaseRequisitions?.data?.results || []}
          columns={columns}
          enableToolbar={true}
          enableExportToExcel={true}
          enablePagination={true}
          enableColumnFilters={true}
          enableSorting={true}
          enablePrintPdf={true}
          tableClassName="border-collapse"
          tHeadClassName="bg-gray-50"
          tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
          tBodyTrClassName="hover:bg-gray-50"
          tBodyCellsClassName="border-t"
          searchInput={
            <input
              value={searchInput}
              name="searchInput"
              type="search"
              onChange={(e) =>
                searchDebouncer(e.target.value, setSearchInput, setSearchValue)
              }
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search purchase requisitions..."
            />
          }
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          totalItems={purchaseRequisitions?.data?.total_data || 0}
        />

        {isAddModalOpen && (
          <AddPurchaseRequisition
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
          />
        )}
      </div>
    </Screen>
  );
};

export default PurchaseRequisitionsIndex;
