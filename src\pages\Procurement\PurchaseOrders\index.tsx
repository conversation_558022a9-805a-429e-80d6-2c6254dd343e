import { Screen } from "@/app-components/layout/screen";
import AddPurchaseOrder from "./modals/AddPurchaseOrder";
import { useState } from "react";
import {
  useGetPurchaseOrdersQuery,
  useGetSuppliersQuery,
  useGetStoresQuery,
  useGetCurrenciesQuery,
  useSubmitPurchaseOrderMutation,
  useApprovePurchaseOrderMutation,
  useRejectPurchaseOrderMutation,
  useDeletePurchaseOrderMutation,
  useSendPurchaseOrderEmailMutation,
  useExportPurchaseOrderPDFMutation
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { PurchaseOrder } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { searchDebouncer } from "@/utils/debouncers";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Plus,
  MoreHorizontal,
  Eye,
  Send,
  CheckCircle,
  XCircle,
  Trash2,
  Mail,
  Download,
  Search
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const PurchaseOrdersIndex = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [filters, setFilters] = useState({
    status: "all",
    supplier: "all",
    delivery_location: "all",
    currency: "all",
    search: "",
  });

  // Transform filters for API (remove "all" values)
  const apiFilters = Object.fromEntries(
    Object.entries(filters).filter(([key, value]) => value !== "all" && value !== "")
  );

  // API queries
  const { data: purchaseOrdersData, isLoading } = useGetPurchaseOrdersQuery(apiFilters);
  const { data: suppliers } = useGetSuppliersQuery({});
  const { data: currencies } = useGetCurrenciesQuery({});

  // Mutation hooks
  const [submitPurchaseOrder] = useSubmitPurchaseOrderMutation();
  const [approvePurchaseOrder] = useApprovePurchaseOrderMutation();
  const [rejectPurchaseOrder] = useRejectPurchaseOrderMutation();
  const [deletePurchaseOrder] = useDeletePurchaseOrderMutation();
  const [sendPurchaseOrderEmail] = useSendPurchaseOrderEmailMutation();
  const [exportPurchaseOrderPDF] = useExportPurchaseOrderPDFMutation();

  // Handler functions
  const handleSubmit = async (id: number) => {
    try {
      await submitPurchaseOrder(id).unwrap();
      toast.success("Purchase order submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit purchase order");
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await approvePurchaseOrder(id).unwrap();
      toast.success("Purchase order approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve purchase order");
    }
  };

  const handleReject = async (id: number, reason: string) => {
    try {
      await rejectPurchaseOrder({ id, reason }).unwrap();
      toast.success("Purchase order rejected");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to reject purchase order");
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this purchase order?")) {
      try {
        await deletePurchaseOrder(id).unwrap();
        toast.success("Purchase order deleted successfully");
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to delete purchase order");
      }
    }
  };

  const handleSendEmail = async (id: number) => {
    try {
      await sendPurchaseOrderEmail({ id, email_data: {} }).unwrap();
      toast.success("Purchase order sent to supplier");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to send purchase order");
    }
  };

  const handleExportPDF = async (id: number) => {
    try {
      const blob = await exportPurchaseOrderPDF(id).unwrap();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `purchase-order-${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Purchase order exported successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to export purchase order");
    }
  };

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Draft": { variant: "secondary" as const, className: "bg-gray-100 text-gray-800" },
      "Pending Approval": { variant: "secondary" as const, className: "bg-yellow-100 text-yellow-800" },
      "Approved": { variant: "secondary" as const, className: "bg-green-100 text-green-800" },
      "Sent": { variant: "secondary" as const, className: "bg-blue-100 text-blue-800" },
      "Cancelled": { variant: "secondary" as const, className: "bg-red-100 text-red-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Draft"];
    return <Badge variant={config.variant} className={config.className}>{status}</Badge>;
  };

  // Search handler
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  // Table columns
  const columns: ColumnDef<PurchaseOrder>[] = [
    {
      accessorKey: "po_number",
      header: "PO Number",
      cell: ({ row }) => (
        <Link
          to={`/procurement/purchase-orders/${row.original.id}`}
          className="font-medium text-blue-600 hover:text-blue-800"
        >
          {row.original.po_number}
        </Link>
      ),
    },
    {
      accessorKey: "supplier_name",
      header: "Supplier",
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.supplier_name}</div>
        </div>
      ),
    },
    {
      accessorKey: "total_value",
      header: "Total Value",
      cell: ({ row }) => (
        <div className="font-medium">
          {row.original.currency} {row.original.total_value?.toLocaleString()}
        </div>
      ),
    },
    {
      accessorKey: "delivery_date",
      header: "Delivery Date",
      cell: ({ row }) => (
        <div>{row.original.delivery_date ? new Date(row.original.delivery_date).toLocaleDateString() : "-"}</div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => getStatusBadge(row.original.status || "Draft"),
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => (
        <div className="text-sm">
          <div>{new Date(row.original.created_at || "").toLocaleDateString()}</div>
          <div className="text-gray-500">{row.original.created_by_name}</div>
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const purchaseOrder = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to={`/procurement/purchase-orders/${purchaseOrder.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>

              {purchaseOrder.status === "Draft" && (
                <>
                  <DropdownMenuItem onClick={() => handleSubmit(purchaseOrder.id!)}>
                    <Send className="mr-2 h-4 w-4" />
                    Submit for Approval
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDelete(purchaseOrder.id!)}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}

              {purchaseOrder.status === "Pending Approval" && (
                <>
                  <DropdownMenuItem onClick={() => handleApprove(purchaseOrder.id!)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleReject(purchaseOrder.id!, "Rejected")}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </DropdownMenuItem>
                </>
              )}

              {purchaseOrder.status === "Approved" && (
                <DropdownMenuItem onClick={() => handleSendEmail(purchaseOrder.id!)}>
                  <Mail className="mr-2 h-4 w-4" />
                  Send to Supplier
                </DropdownMenuItem>
              )}

              <DropdownMenuItem onClick={() => handleExportPDF(purchaseOrder.id!)}>
                <Download className="mr-2 h-4 w-4" />
                Export PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Purchase Orders</h1>
            <p className="text-gray-600 mt-1">Manage purchase orders and supplier communications</p>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={() => setShowAddModal(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Purchase Order
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search purchase orders..."
              className="w-64"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>

          <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Draft">Draft</SelectItem>
              <SelectItem value="Pending Approval">Pending Approval</SelectItem>
              <SelectItem value="Approved">Approved</SelectItem>
              <SelectItem value="Sent">Sent</SelectItem>
              <SelectItem value="Cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.supplier} onValueChange={(value) => setFilters(prev => ({ ...prev, supplier: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by supplier" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Suppliers</SelectItem>
              {suppliers?.data?.results?.map((supplier: any) => (
                <SelectItem key={supplier.id} value={supplier.id.toString()}>
                  {supplier.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.currency} onValueChange={(value) => setFilters(prev => ({ ...prev, currency: value }))}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Currency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {currencies?.data?.results?.map((currency: any) => (
                <SelectItem key={currency.id} value={currency.code}>
                  {currency.code}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Data Table */}
        <DataTable
          columns={columns}
          data={purchaseOrdersData?.data?.results || []}
          loading={isLoading}
        />

        {/* Add Purchase Order Modal */}
        <AddPurchaseOrder
          open={showAddModal}
          onClose={() => setShowAddModal(false)}
        />
      </div>
    </Screen>
  );
};

export default PurchaseOrdersIndex;
