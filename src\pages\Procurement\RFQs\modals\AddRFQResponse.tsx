import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Building, FileText, Upload, DollarSign } from "lucide-react";
import {
  useCreateRFQResponseMutation,
  useUploadRFQResponseDocumentMutation,
  useGetRFQQuery,
  useGetSuppliersQuery,
  useGetCurrenciesQuery,
} from "@/redux/slices/procurement";
import { RFQResponseFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddRFQResponseProps {
  open: boolean;
  onClose: () => void;
  rfqId: number;
}

const AddRFQResponse = ({ open, onClose, rfqId }: AddRFQResponseProps) => {
  const [createResponse, { isLoading: creating }] = useCreateRFQResponseMutation();
  const [uploadDocument, { isLoading: uploading }] = useUploadRFQResponseDocumentMutation();
  
  // Fetch supporting data
  const { data: rfq } = useGetRFQQuery(rfqId);
  const { data: suppliers } = useGetSuppliersQuery({});
  const { data: currencies } = useGetCurrenciesQuery({});

  const [formData, setFormData] = useState<RFQResponseFormData>({
    rfq: rfqId,
    supplier: 0,
    payment_terms: "",
    delivery_time_days: "",
    currency: "USD",
    notes: "",
    document: undefined,
    items: [],
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleInputChange = (field: keyof RFQResponseFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFormData(prev => ({ ...prev, document: file }));
    }
  };

  const calculateTotalValue = () => {
    return formData.items.reduce((total, item) => {
      const quantity = rfq?.items?.find(rfqItem => rfqItem.product === item.product)?.quantity || 0;
      const unitPrice = typeof item.unit_price === 'string' ? parseFloat(item.unit_price) || 0 : item.unit_price;
      return total + (quantity * unitPrice);
    }, 0);
  };

  const resetForm = () => {
    setFormData({
      rfq: rfqId,
      supplier: 0,
      payment_terms: "",
      delivery_time_days: "",
      currency: "USD",
      notes: "",
      document: undefined,
      items: rfq?.items?.map(item => ({
        product: item.product || 0,
        unit_price: "",
        delivery_time_days: "",
        notes: "",
      })) || [],
    });
    setSelectedFile(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.supplier) {
      toast.error("Please select a supplier");
      return;
    }

    const validItems = formData.items.filter(
      (item) => item.unit_price && item.unit_price !== ""
    );

    if (validItems.length === 0) {
      toast.error("Please provide unit prices for at least one item");
      return;
    }

    try {
      const payload = {
        ...formData,
        items: validItems,
        total_value: calculateTotalValue(),
      };

      const response = await createResponse(payload).unwrap();
      
      // Upload document if provided
      if (selectedFile && response.id) {
        try {
          await uploadDocument({ id: response.id, document: selectedFile }).unwrap();
        } catch (uploadError) {
          toast.error("Response created but document upload failed");
        }
      }

      toast.success("RFQ response created successfully");
      resetForm();
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create RFQ response");
    }
  };

  useEffect(() => {
    if (open && rfq) {
      setFormData(prev => ({
        ...prev,
        items: rfq.items?.map(item => ({
          product: item.product || 0,
          unit_price: "",
          delivery_time_days: "",
          notes: "",
        })) || [],
      }));
    }
  }, [open, rfq]);

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Add RFQ Response - {rfq?.rfq_number}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Building className="h-4 w-4" />
                Supplier Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="supplier">Supplier *</Label>
                  <Select
                    value={formData.supplier ? formData.supplier.toString() : ""}
                    onValueChange={(value) => handleInputChange("supplier", parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliers?.data?.results?.map((supplier: any) => (
                        <SelectItem key={supplier.id} value={supplier.id.toString()}>
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select
                    value={formData.currency}
                    onValueChange={(value) => handleInputChange("currency", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      {currencies?.data?.results?.map((currency: any) => (
                        <SelectItem key={currency.id} value={currency.code}>
                          {currency.code} - {currency.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="payment_terms">Payment Terms</Label>
                  <Input
                    id="payment_terms"
                    placeholder="e.g., Net 30 days"
                    value={formData.payment_terms}
                    onChange={(e) => handleInputChange("payment_terms", e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="delivery_time_days">Delivery Time (Days)</Label>
                  <Input
                    id="delivery_time_days"
                    type="number"
                    min="1"
                    placeholder="e.g., 14"
                    value={formData.delivery_time_days}
                    onChange={(e) => handleInputChange("delivery_time_days", e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Items Pricing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <DollarSign className="h-4 w-4" />
                Item Pricing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {rfq?.items?.map((rfqItem, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{rfqItem.product_name}</h4>
                      <p className="text-sm text-gray-600">
                        Quantity: {rfqItem.quantity} {rfqItem.unit_of_measure_name}
                      </p>
                      {rfqItem.specifications && (
                        <p className="text-sm text-gray-500 mt-1">
                          Specs: {rfqItem.specifications}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label>Unit Price *</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        value={formData.items[index]?.unit_price || ""}
                        onChange={(e) => handleItemChange(index, "unit_price", e.target.value)}
                      />
                    </div>

                    <div>
                      <Label>Delivery Time (Days)</Label>
                      <Input
                        type="number"
                        min="1"
                        placeholder="Days"
                        value={formData.items[index]?.delivery_time_days || ""}
                        onChange={(e) => handleItemChange(index, "delivery_time_days", e.target.value)}
                      />
                    </div>

                    <div>
                      <Label>Total Price</Label>
                      <div className="p-2 bg-gray-50 rounded border text-sm font-medium">
                        {formData.currency} {(
                          (rfqItem.quantity || 0) * 
                          (parseFloat(formData.items[index]?.unit_price as string) || 0)
                        ).toLocaleString()}
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label>Item Notes</Label>
                    <Textarea
                      placeholder="Any specific notes for this item..."
                      value={formData.items[index]?.notes || ""}
                      onChange={(e) => handleItemChange(index, "notes", e.target.value)}
                      rows={2}
                    />
                  </div>
                </div>
              ))}

              {/* Total Summary */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center text-lg font-bold">
                  <span>Total Response Value:</span>
                  <span>{formData.currency} {calculateTotalValue().toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Document Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Upload className="h-4 w-4" />
                Supporting Document
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="document">Upload Response Document (Optional)</Label>
                <Input
                  id="document"
                  type="file"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  onChange={handleFileChange}
                  className="mt-1"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Supported formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB)
                </p>
              </div>

              {selectedFile && (
                <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                  <FileText className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{selectedFile.name}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedFile(null);
                      setFormData(prev => ({ ...prev, document: undefined }));
                    }}
                  >
                    Remove
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Additional Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Additional Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="notes">Response Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes or comments about this response..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating || uploading}>
              {creating || uploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {creating ? "Creating..." : "Uploading..."}
                </>
              ) : (
                "Submit Response"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddRFQResponse;
