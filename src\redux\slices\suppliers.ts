import { apiSlice } from "../apiSlice";

export const supplierApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getSuppliers: builder.query({
      query: (params) => ({
        url: "/suppliers",
        method: "GET",
        params: params,
      }),
    }),

    retrieveSupplier: builder.query({
      query: (id) => ({
        url: `/suppliers/${id}`,
        method: "GET",
      }),
    }),

    addSuppliers: builder.mutation({
      query: (payload) => ({
        url: "/suppliers",
        method: "POST",
        body: payload,
      }),
    }),

    patchSuppliers: builder.mutation({
      query: (payload) => ({
        url: `/suppliers/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
  useGetSuppliersQuery,
  useRetrieveSupplierQuery,
  useAddSuppliersMutation,
  usePatchSuppliersMutation,

  useLazyGetSuppliersQuery,
  useLazyRetrieveSupplierQuery,
} = supplierApiSlice;
