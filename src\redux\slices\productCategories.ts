import {
  ProductCategory,
  ProductMainCategory,
  ProductMainCategoryApiResponse,
} from "@/types/products";
import { apiSlice } from "../apiSlice";

export const ProductCategoryApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getProductMainCategory: builder.query({
      query: (params) => ({
        url: "/ProductMainCategory",
        method: "GET",
        params: params,
      }),
    }),

    retrieveProductMainCategory: builder.query({
      query: (id) => ({
        url: `/ProductMainCategory/${id}`,
        method: "GET",
      }),
    }),

    addProductMainCategory: builder.mutation({
      query: (payload) => ({
        url: "/ProductMainCategory",
        method: "POST",
        body: payload,
      }),
    }),

    patchProductMainCategory: builder.mutation({
      query: (payload) => ({
        url: `/ProductMainCategory/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),

    getProductCategory: builder.query({
      query: (params) => ({
        url: "/ProductCategory",
        method: "GET",
        params: params,
      }),
    }),

    retrieveProductCategory: builder.query({
      query: (id) => ({
        url: `/ProductCategory/${id}`,
        method: "GET",
      }),
    }),

    addProductCategory: builder.mutation({
      query: (payload) => ({
        url: "/ProductCategory",
        method: "POST",
        body: payload,
      }),
    }),

    patchProductCategory: builder.mutation({
      query: (payload) => ({
        url: `/ProductCategory/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
  useGetProductMainCategoryQuery,
  useRetrieveProductMainCategoryQuery,
  useAddProductMainCategoryMutation,
  usePatchProductMainCategoryMutation,

  useGetProductCategoryQuery,
  useRetrieveProductCategoryQuery,
  useAddProductCategoryMutation,
  usePatchProductCategoryMutation,

  useLazyGetProductMainCategoryQuery,
  useLazyRetrieveProductMainCategoryQuery,
  useLazyGetProductCategoryQuery,
  useLazyRetrieveProductCategoryQuery,
} = ProductCategoryApiSlice;
