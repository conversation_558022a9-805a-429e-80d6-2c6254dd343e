import { useState } from 'react';
import { <PERSON>, <PERSON>Off, Loader, Lock, Mail, Play } from 'lucide-react';
import Logo from '@/assets/logo.png';
import { useForm } from 'react-hook-form';
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod";
import { usePostLoginMutation } from '@/redux/slices/auth';
import { toast } from '@/components/custom/Toast/MyToast';
import { Link, useLocation, useNavigate } from 'react-router-dom';

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false);


  const navigate = useNavigate();
  const location = useLocation();
  // Get the intended destination from location state
  const from = location.state?.from || '/';


  // form
  const formSchema = z.object({
    username: z.string().min(1, { message: "Email is required.", }).email({ message: "Invalid email format." }),
    password: z.string().min(1, { message: "Password is required.", }),
  })

  const { register, handleSubmit, watch, formState: { errors } } = useForm<z.infer<typeof formSchema>>({ resolver: zodResolver(formSchema), });
  const [postLogin, { isLoading: isSignInLoading }] = usePostLoginMutation();
  const onSubmit = async (data: any) => {
    try {
      await postLogin(data).unwrap();
      toast.success('Login successful');
      navigate(from, { replace: true });
      // console.log('Login successful:', response);
    } catch (err: any) {
      const { data } = err
      const { non_field_errors, username } = data
      toast.error(Array.isArray(non_field_errors) && non_field_errors[0] || Array.isArray(username) && username[0] || 'Something went wrong!')
      // console.error('Login failed:', err);
    }
  };


  return (<>
  {/* Right Panel - Login Form */}
  <div className="w-full min-h-screen lg:w-1/2 flex items-center justify-center bg-white px-4">
    <div className="w-full max-w-md flex flex-col items-center gap-8">
      {/* Logo */}
      <div className="flex justify-center mt-6 mb-2">
        <img src={Logo} alt="GMC Logo" className="h-16 w-auto" />
      </div>

      {/* Header */}
      <div className="text-center w-full">
        <h2 className="text-2xl font-bold text-gray-900 mb-1">Welcome Back!</h2>
        <p className="text-gray-500 text-sm mb-6">
          Enter your email and password to access your account
        </p>
      </div>

      {/* Login Form */}
      <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
        {/* Email */}
        <div className="mb-4">
          <label className="block text-sm text-gray-700 mb-1" htmlFor="email">Email</label>
          <input
            {...register("username")}
            id="email"
            type="email"
            placeholder="Enter your email"
            className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
          />
          {errors.username && (
            <p className="text-red-500 text-xs mt-1">{errors.username.message}</p>
          )}
        </div>

        {/* Password */}
        <div className="mb-2">
          <label className="block text-sm text-gray-700 mb-1" htmlFor="password">Password</label>
          <div className="relative">
            <input
              {...register("password")}
              id="password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Enter your password"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500 pr-10"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              tabIndex={-1}
            >
              {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          {errors.password && (
            <p className="text-red-500 text-xs mt-1">{errors.password.message}</p>
          )}
        </div>

        {/* Forgot Password & Remember Me */}
        <div className="flex items-center justify-between mb-6">
          <label className="flex items-center gap-2 text-sm text-gray-600">
            <input type="checkbox" className="accent-red-600" />
            Remember me
          </label>
          <Link
            to="/auth/forgot-password"
            className="text-red-500 hover:text-red-600 text-sm"
          >
            Forgot Password?
          </Link>
        </div>

        {/* Sign In Button */}
        <button
          className="w-full bg-black text-white font-semibold py-2 rounded-md hover:bg-gray-900 transition-colors mb-2"
          type="submit"
          disabled={isSignInLoading}
        >
          {isSignInLoading ? (
            <span className='flex items-center justify-center gap-2'>
              <Loader className="animate-spin" size={22} />
              SIGNING IN...
            </span>
          ) : (
            <span>SIGN IN</span>
          )}
        </button>
      </form>

      {/* Sign Up Link */}
      <div className="text-center w-full">
        <span className="text-gray-500 text-sm">Don&apos;t have an account?</span>{" "}
        <Link to="/auth/register" className="text-red-500 hover:underline text-sm font-medium">
          Sign Up
        </Link>
      </div>
    </div>
  </div>
</>
);
};

export default LoginPage;