import { apiSlice } from "../apiSlice";

// Types for Revenue Center API
export interface RevenueCenter {
  id?: number;
  name: string;
  code: string;
  branch: string;
  description?: string;
  is_active?: boolean;
  sales_categories?: string[];
  tax_class?: string;
  service_charge_applies?: boolean;
  service_charge_rate?: number;
}

export const revenueCenterApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all revenue centers
    getRevenueCenters: builder.query<RevenueCenter[], any>({
      query: (params) => ({
        url: "/setup/revenue-centers",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log('Raw revenue centers API response:', response);
        
        // Handle different response structures
        if (Array.isArray(response)) {
          console.log('Response is direct array:', response);
          return response;
        } else if (response && response.data) {
          console.log('Response has data property:', response.data);
          if (Array.isArray(response.data)) {
            return response.data;
          } else if (response.data.revenue_centers && Array.isArray(response.data.revenue_centers)) {
            return response.data.revenue_centers;
          } else if (response.data.results && Array.isArray(response.data.results)) {
            return response.data.results;
          } else {
            console.log('Wrapping single object in array:', response.data);
            return [response.data];
          }
        } else if (response && response.results && Array.isArray(response.results)) {
          console.log('Response has results property:', response.results);
          return response.results;
        } else {
          console.warn('Unexpected revenue centers API response structure:', response);
          console.log('Returning empty array as fallback');
          return [];
        }
      },
      providesTags: ["RevenueCenters"],
    }),

    // Get single revenue center
    getRevenueCenter: builder.query<RevenueCenter, string>({
      query: (id) => ({
        url: `/setup/revenue-centers/${id}`,
        method: "GET",
      }),
      transformResponse: (response: any) => {
        console.log('Get revenue center response:', response);
        if (response && response.data) {
          return response.data;
        }
        return response;
      },
      providesTags: (result, error, id) => [{ type: "RevenueCenters", id }],
    }),

    // Create revenue center
    createRevenueCenter: builder.mutation<RevenueCenter, Partial<RevenueCenter>>({
      query: (payload) => ({
        url: "/setup/revenue-centers",
        method: "POST",
        body: payload,
      }),
      transformResponse: (response: any) => {
        console.log('Create revenue center response:', response);
        if (response && response.data) {
          return response.data;
        }
        return response;
      },
      invalidatesTags: ["RevenueCenters"],
    }),

    // Update revenue center
    updateRevenueCenter: builder.mutation<RevenueCenter, { id: string; data: Partial<RevenueCenter> }>({
      query: ({ id, data }) => ({
        url: `/setup/revenue-centers/${id}`,
        method: "PATCH",
        body: data,
      }),
      transformResponse: (response: any) => {
        console.log('Update revenue center response:', response);
        if (response && response.data) {
          return response.data;
        }
        return response;
      },
      invalidatesTags: (result, error, { id }) => [{ type: "RevenueCenters", id }, "RevenueCenters"],
    }),

    // Delete revenue center
    deleteRevenueCenter: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/revenue-centers/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["RevenueCenters"],
    }),
  }),
});

export const { 
  useGetRevenueCentersQuery, 
  useGetRevenueCenterQuery,
  useCreateRevenueCenterMutation,
  useUpdateRevenueCenterMutation,
  useDeleteRevenueCenterMutation,
} = revenueCenterApiSlice;
