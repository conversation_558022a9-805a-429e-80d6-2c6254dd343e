import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Trash2, Loader2, Package, Building, MapPin, CreditCard } from "lucide-react";
import {
  useCreatePurchaseOrderMutation,
  useGetSuppliersQuery,
  useGetStoresQuery,
  useGetProductsQuery,
  useGetUnitsOfMeasureQuery,
  useGetPaymentTermsQuery,
  useGetCurrenciesQuery,
} from "@/redux/slices/procurement";
import { PurchaseOrderFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddPurchaseOrderProps {
  open: boolean;
  onClose: () => void;
}

const AddPurchaseOrder = ({ open, onClose }: AddPurchaseOrderProps) => {
  const [createPurchaseOrder, { isLoading: creating }] = useCreatePurchaseOrderMutation();
  
  // Fetch supporting data
  const { data: suppliers } = useGetSuppliersQuery({});
  const { data: stores } = useGetStoresQuery({});
  const { data: products } = useGetProductsQuery({});
  const { data: unitsOfMeasure } = useGetUnitsOfMeasureQuery({});
  const { data: paymentTerms } = useGetPaymentTermsQuery({});
  const { data: currencies } = useGetCurrenciesQuery({});

  const [formData, setFormData] = useState<PurchaseOrderFormData>({
    supplier: "",
    delivery_location: "",
    delivery_address: "",
    payment_terms: "",
    delivery_date: "",
    currency: "USD",
    tax_rate: "",
    notes: "",
    terms_and_conditions: "",
    items: [
      {
        product: "",
        quantity: "",
        unit_of_measure: "",
        unit_price: "",
      },
    ],
  });

  const handleInputChange = (field: keyof PurchaseOrderFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const addItem = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          product: "",
          quantity: "",
          unit_of_measure: "",
          unit_price: "",
        },
      ],
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      setFormData((prev) => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index),
      }));
    }
  };

  const calculateItemTotal = (quantity: number | string, unitPrice: number | string) => {
    const qty = typeof quantity === 'string' ? parseFloat(quantity) || 0 : quantity;
    const price = typeof unitPrice === 'string' ? parseFloat(unitPrice) || 0 : unitPrice;
    return qty * price;
  };

  const calculateSubtotal = () => {
    return formData.items.reduce((total, item) => {
      return total + calculateItemTotal(item.quantity, item.unit_price);
    }, 0);
  };

  const calculateTax = () => {
    const subtotal = calculateSubtotal();
    const taxRate = typeof formData.tax_rate === 'string' ? parseFloat(formData.tax_rate) || 0 : formData.tax_rate;
    return (subtotal * taxRate) / 100;
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax();
  };

  const resetForm = () => {
    setFormData({
      supplier: "",
      delivery_location: "",
      delivery_address: "",
      payment_terms: "",
      delivery_date: "",
      currency: "USD",
      tax_rate: "",
      notes: "",
      terms_and_conditions: "",
      items: [
        {
          product: "",
          quantity: "",
          unit_of_measure: "",
          unit_price: "",
        },
      ],
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.supplier) {
      toast.error("Please select a supplier");
      return;
    }

    if (!formData.delivery_location) {
      toast.error("Please select a delivery location");
      return;
    }

    if (!formData.delivery_date) {
      toast.error("Please select a delivery date");
      return;
    }

    const validItems = formData.items.filter(
      (item) => item.product && item.quantity && item.unit_price
    );

    if (validItems.length === 0) {
      toast.error("Please add at least one valid item");
      return;
    }

    try {
      const payload = {
        ...formData,
        items: validItems,
        subtotal: calculateSubtotal(),
        tax_amount: calculateTax(),
        total_value: calculateTotal(),
      };

      await createPurchaseOrder(payload).unwrap();
      toast.success("Purchase order created successfully");
      resetForm();
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create purchase order");
    }
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Create Purchase Order
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Building className="h-4 w-4" />
                Supplier Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="supplier">Supplier *</Label>
                  <Select
                    value={formData.supplier ? formData.supplier.toString() : ""}
                    onValueChange={(value) => handleInputChange("supplier", parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliers?.data?.results?.map((supplier: any) => (
                        <SelectItem key={supplier.id} value={supplier.id.toString()}>
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="currency">Currency *</Label>
                  <Select
                    value={formData.currency}
                    onValueChange={(value) => handleInputChange("currency", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      {currencies?.data?.results?.map((currency: any) => (
                        <SelectItem key={currency.id} value={currency.code}>
                          {currency.code} - {currency.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <MapPin className="h-4 w-4" />
                Delivery Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="delivery_location">Delivery Location *</Label>
                  <Select
                    value={formData.delivery_location ? formData.delivery_location.toString() : ""}
                    onValueChange={(value) => handleInputChange("delivery_location", parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select delivery location" />
                    </SelectTrigger>
                    <SelectContent>
                      {stores?.data?.results?.map((store: any) => (
                        <SelectItem key={store.id} value={store.id.toString()}>
                          {store.name} - {store.location}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="delivery_date">Expected Delivery Date *</Label>
                  <Input
                    id="delivery_date"
                    type="date"
                    value={formData.delivery_date}
                    onChange={(e) => handleInputChange("delivery_date", e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="delivery_address">Delivery Address</Label>
                <Textarea
                  id="delivery_address"
                  placeholder="Enter specific delivery address if different from location..."
                  value={formData.delivery_address}
                  onChange={(e) => handleInputChange("delivery_address", e.target.value)}
                  rows={2}
                />
              </div>
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <CreditCard className="h-4 w-4" />
                Payment Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="payment_terms">Payment Terms</Label>
                  <Select
                    value={formData.payment_terms}
                    onValueChange={(value) => handleInputChange("payment_terms", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment terms" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentTerms?.data?.results?.map((term: any) => (
                        <SelectItem key={term.id} value={term.name}>
                          {term.name} - {term.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="tax_rate">Tax Rate (%)</Label>
                  <Input
                    id="tax_rate"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    placeholder="0.00"
                    value={formData.tax_rate}
                    onChange={(e) => handleInputChange("tax_rate", e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Package className="h-4 w-4" />
                Order Items
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    {formData.items.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <Label>Product *</Label>
                      <Select
                        value={item.product ? item.product.toString() : ""}
                        onValueChange={(value) => handleItemChange(index, "product", parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {products?.data?.results?.map((product: any) => (
                            <SelectItem key={product.id} value={product.id.toString()}>
                              {product.name} ({product.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Quantity *</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, "quantity", e.target.value)}
                      />
                    </div>

                    <div>
                      <Label>Unit of Measure *</Label>
                      <Select
                        value={item.unit_of_measure ? item.unit_of_measure.toString() : ""}
                        onValueChange={(value) => handleItemChange(index, "unit_of_measure", parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {unitsOfMeasure?.data?.results?.map((unit: any) => (
                            <SelectItem key={unit.id} value={unit.id.toString()}>
                              {unit.name} ({unit.abbreviation})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Unit Price *</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        value={item.unit_price}
                        onChange={(e) => handleItemChange(index, "unit_price", e.target.value)}
                      />
                    </div>
                  </div>

                  {item.quantity && item.unit_price && (
                    <div className="text-right">
                      <span className="text-sm text-gray-600">Item Total: </span>
                      <span className="font-medium">
                        {formData.currency} {calculateItemTotal(item.quantity, item.unit_price).toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addItem}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>

              {/* Order Summary */}
              <div className="border-t pt-4 space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span className="font-medium">
                    {formData.currency} {calculateSubtotal().toLocaleString()}
                  </span>
                </div>
                {formData.tax_rate && (
                  <div className="flex justify-between">
                    <span>Tax ({formData.tax_rate}%):</span>
                    <span className="font-medium">
                      {formData.currency} {calculateTax().toLocaleString()}
                    </span>
                  </div>
                )}
                <div className="flex justify-between text-lg font-bold border-t pt-2">
                  <span>Total:</span>
                  <span>
                    {formData.currency} {calculateTotal().toLocaleString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notes and Terms */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Additional Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes or instructions..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="terms_and_conditions">Terms and Conditions</Label>
                <Textarea
                  id="terms_and_conditions"
                  placeholder="Enter terms and conditions for this purchase order..."
                  value={formData.terms_and_conditions}
                  onChange={(e) => handleInputChange("terms_and_conditions", e.target.value)}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Purchase Order"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddPurchaseOrder;
