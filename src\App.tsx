import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ToastProvider } from "./components/custom/Toast/MyToast";
import { useTheme } from "./hooks/use-theme";
import Home from "./pages/Home";
import AuthIndex from "./pages/auth";
import LoginPage from "./pages/auth/SignIn";
import LockScreenPage from "./pages/auth/LockScreen";
import ForgetPassword from "./pages/auth/ForgetPassword";
import Reset from "./pages/auth/Reset";
import SignupPage from "./pages/auth/SignUp";
import OtpVerifyPage from "./pages/auth/CodeVerify";
import MaintenancePage from "./pages/errorpages/maintainence";
import Error404Page from "./pages/errorpages/error-404";
import Error500Page from "./pages/errorpages/error-500";

// setup pages
import Suppliers from "./pages/setups/Supplier";
import SupplierDetails from "./pages/setups/Supplier/SupplierDetails";
import CostCenter from "./pages/setups/CostCenter";
import Stores from "./pages/setups/Store";
import UoM from "./pages/setups/UnitOfMeasure";
import ProductMainCategoryPage from "./pages/setups/ProductCategoriesSettings/ProductMainCategoryPage";
import ProductMainCategoryDetails from "./pages/setups/ProductCategoriesSettings/ProductMainCategoryDetails";

import ApiDebugPanel from "./components/debug/ApiDebugPanel";

import ProcurementDashboard from "./pages/Procurement";
import StoreRequisitions from "./pages/Procurement/StoreRequisitions";
import StoreRequisitionDetail from "./pages/Procurement/StoreRequisitions/StoreRequisitionDetail";
import PurchaseRequisitions from "./pages/Procurement/PurchaseRequisitions";
import PurchaseRequisitionDetail from "./pages/Procurement/PurchaseRequisitions/PurchaseRequisitionDetail";
import FoodMenu from "./pages/menuhandler/MenuPage";
import FoodCombo from "./pages/menuhandler/Combo Meals/ComboMeal";
import GuestChecksUI from "./pages/GuestCheckIn/GuestCheck";

// POS Configuration Pages
import POSConfiguration from "./pages/pos";
import POSDashboard from "./pages/pos/Dashboard";
import BranchManagement from "./pages/pos/branches";
import NewBranch from "./pages/pos/branches/NewBranch";
import EditBranch from "./pages/pos/branches/EditBranch";
import BranchDetail from "./pages/pos/branches/BranchDetail";
import RevenueCenterManagement from "./pages/pos/revenue-centers";
import NewRevenueCenter from "./pages/pos/revenue-centers/NewRevenueCenter";
import EditRevenueCenter from "./pages/pos/revenue-centers/EditRevenueCenter";
import RevenueCenterDetail from "./pages/pos/revenue-centers/RevenueCenterDetail";
import WorkstationManagement from "./pages/pos/workstations";
import NewWorkstation from "./pages/pos/workstations/NewWorkstation";
import EditWorkstation from "./pages/pos/workstations/EditWorkstation";
import WorkstationDetail from "./pages/pos/workstations/WorkstationDetail";
import TaxConfiguration from "./pages/pos/tax-configuration";
import NewTaxClass from "./pages/pos/tax-configuration/NewTaxClass";
import NewTaxRate from "./pages/pos/tax-configuration/NewTaxRate";
import PrinterManagement from "./pages/pos/printers";
import NewPrinter from "./pages/pos/printers/NewPrinter";
import EditPrinter from "./pages/pos/printers/EditPrinter";

// Admin/Users Management Pages
import UserManagement from "./pages/users";
import UserGroups from "./pages/users/UserGroups";
import UserPermissions from "./pages/users/UserPermissions";
import UserPermissionsMatrix from "./pages/users/UserPermissionsMatrix";
import GroupsPermissions from "./pages/users/GroupsPermissions";
import UserGroupsMatrix from "./pages/users/UserGroupsMatrix";
import EmployeeProfile from "./pages/users/EmployeeProfile";
import ShiftManagement from "./pages/users/ShiftManagement";
import SystemSettings from "./pages/admin/SystemSettings";

import PurchaseOrders from "./pages/Procurement/PurchaseOrders";
import PurchaseOrderDetail from "./pages/Procurement/PurchaseOrders/PurchaseOrderDetail";
import RFQs from "./pages/Procurement/RFQs";
import RFQDetail from "./pages/Procurement/RFQs/RFQDetail";
import BidAnalysis from "./pages/Procurement/BidAnalysis";
import BidAnalysisDetail from "./pages/Procurement/BidAnalysis/BidAnalysisDetail";

function App() {
  const { theme } = useTheme();

  return (
    <>
      <ToastProvider theme={theme} position="top-right" />

      <Router>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/auth" element={<AuthIndex />}>
            <Route path="/auth/login" element={<LoginPage />} />
            <Route path="/auth/register" element={<SignupPage />} />
            <Route path="/auth/lockscreen" element={<LockScreenPage />} />
            <Route path="/auth/forgot-password" element={<ForgetPassword />} />
            <Route path="/auth/reset-password" element={<Reset />} />
            <Route path="/auth/verify-otp" element={<OtpVerifyPage />} />
          </Route>
          <Route path="/maintenance" element={<MaintenancePage />} />
          <Route path="/error/404" element={<Error404Page />} />
          <Route path="/error/500" element={<Error500Page />} />

          {/* settings and Congigurations  routes */}
          <Route path="/suppliers" element={<Suppliers />} />
          <Route path="/suppliers/:id" element={<SupplierDetails />} />
          <Route path="/cost-centers" element={<CostCenter />} />
          <Route path="/stores" element={<Stores />} />
          <Route path="/units-of-measure" element={<UoM />} />
          <Route
            path="/product-categories"
            element={<ProductMainCategoryPage />}
          />
          <Route
            path="/product-categories/:id"
            element={<ProductMainCategoryDetails />}
          />
          {/* Food Menu */}
          <Route path="/Menu" element={<FoodMenu />} />
          <Route path="/Combo" element={<FoodCombo />} />
          <Route path="/CheckIn" element={<GuestChecksUI />} />
          {/* Procurement Management routes */}
          <Route path="/procurement" element={<ProcurementDashboard />} />
          <Route
            path="/procurement/store-requisitions"
            element={<StoreRequisitions />}
          />
          <Route
            path="/procurement/store-requisitions/:id"
            element={<StoreRequisitionDetail />}
          />
          <Route
            path="/procurement/purchase-requisitions"
            element={<PurchaseRequisitions />}
          />
          <Route
            path="/procurement/purchase-requisitions/:id"
            element={<PurchaseRequisitionDetail />}
          />

          {/* POS Configuration routes */}
          <Route path="/pos" element={<POSConfiguration />}>
            <Route index element={<POSDashboard />} />

            {/* Branch Management routes */}
            <Route path="branches" element={<BranchManagement />} />
            <Route path="branches/new" element={<NewBranch />} />
            <Route path="branches/:id" element={<BranchDetail />} />
            <Route path="branches/:id/edit" element={<EditBranch />} />

            {/* Revenue Center routes */}
            <Route
              path="revenue-centers"
              element={<RevenueCenterManagement />}
            />
            <Route path="revenue-centers/new" element={<NewRevenueCenter />} />
            <Route
              path="revenue-centers/:id"
              element={<RevenueCenterDetail />}
            />
            <Route
              path="revenue-centers/:id/edit"
              element={<EditRevenueCenter />}
            />

            {/* Workstation routes */}
            <Route path="workstations" element={<WorkstationManagement />} />
            <Route path="workstations/new" element={<NewWorkstation />} />
            <Route path="workstations/:id" element={<WorkstationDetail />} />
            <Route path="workstations/:id/edit" element={<EditWorkstation />} />

            {/* Tax Configuration routes */}
            <Route path="tax-configuration" element={<TaxConfiguration />} />
            <Route
              path="tax-configuration/classes/new"
              element={<NewTaxClass />}
            />
            <Route
              path="tax-configuration/rates/new"
              element={<NewTaxRate />}
            />

            {/* Printer Management routes */}
            <Route path="printers" element={<PrinterManagement />} />
            <Route path="printers/new" element={<NewPrinter />} />
            <Route path="printers/:id/edit" element={<EditPrinter />} />
          </Route>

          {/* Admin/Users Management routes */}
          <Route path="/admin/users" element={<UserManagement />} />
          <Route path="/admin/users/groups" element={<UserGroups />} />
          <Route
            path="/admin/users/permissions"
            element={<UserPermissions />}
          />
          <Route
            path="/admin/users/permissions-matrix"
            element={<UserPermissionsMatrix />}
          />
          <Route
            path="/admin/users/groups-permissions"
            element={<GroupsPermissions />}
          />
          <Route
            path="/admin/users/groups-matrix"
            element={<UserGroupsMatrix />}
          />

          {/* Individual User Management routes */}
          <Route path="/admin/users/:id" element={<UserManagement />} />
          <Route path="/admin/users/:id/edit" element={<UserManagement />} />
          <Route
            path="/admin/users/:id/permissions"
            element={<UserPermissions />}
          />
          <Route path="/admin/users/groups/:id" element={<UserGroups />} />
          <Route path="/admin/users/groups/:id/edit" element={<UserGroups />} />
          <Route path="/admin/users/groups/:id/head" element={<UserGroups />} />
          <Route
            path="/admin/users/groups/:id/add-member"
            element={<UserGroups />}
          />
          <Route
            path="/admin/users/permissions/:id/edit"
            element={<UserPermissions />}
          />
          <Route
            path="/admin/users/permissions/:id/users"
            element={<UserPermissions />}
          />

          {/* Employee Management routes */}
          <Route path="/admin/employees" element={<EmployeeProfile />} />
          <Route path="/admin/employees/:id" element={<EmployeeProfile />} />
          <Route
            path="/admin/employees/:id/edit"
            element={<EmployeeProfile />}
          />
          <Route
            path="/admin/employees/:id/shifts"
            element={<ShiftManagement />}
          />
          <Route path="/admin/shifts" element={<ShiftManagement />} />

          {/* System Settings routes */}
          <Route path="/admin/settings" element={<SystemSettings />} />

          {/* Procurement Management routes */}
          <Route path="/procurement" element={<ProcurementDashboard />} />
          <Route
            path="/procurement/store-requisitions"
            element={<StoreRequisitions />}
          />
          <Route
            path="/procurement/store-requisitions/:id"
            element={<StoreRequisitionDetail />}
          />
          <Route
            path="/procurement/purchase-requisitions"
            element={<PurchaseRequisitions />}
          />
          <Route
            path="/procurement/purchase-requisitions/:id"
            element={<PurchaseRequisitionDetail />}
          />
          <Route
            path="/procurement/purchase-orders"
            element={<PurchaseOrders />}
          />
          <Route
            path="/procurement/purchase-orders/:id"
            element={<PurchaseOrderDetail />}
          />
          <Route path="/procurement/rfqs" element={<RFQs />} />
          <Route path="/procurement/rfqs/:id" element={<RFQDetail />} />
          <Route path="/procurement/bid-analysis" element={<BidAnalysis />} />
          <Route
            path="/procurement/bid-analysis/:id"
            element={<BidAnalysisDetail />}
          />
        </Routes>

        {/* Debug Panel - only in development */}
        {import.meta.env.DEV && <ApiDebugPanel />}
      </Router>
    </>
  );
}

export default App;
