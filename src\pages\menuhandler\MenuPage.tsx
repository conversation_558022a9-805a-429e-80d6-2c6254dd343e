import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import React, { useState } from "react";
import { MealCard } from "./Components/MealCard";
import { CreateMenuItemModal } from "./Components/AddFoodModal";


const initialMeals = [
  {
    title: "Breakfast",
    description: "Start your day with energy.",
    imageUrl: "https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=400&q=80",
    price: "$8.50",
    category: "Mains",
    rating: 4.8,
    prepTime: "5 mins",
  },
  {
    title: "Beverages",
    description: "Refreshing drinks for every mood.",
    imageUrl: "https://images.unsplash.com/photo-1510626176961-4b57d4fbad04?auto=format&fit=crop&w=400&q=80",
    price: "$3.50",
    category: "Beverages",
    rating: 4.7,
    prepTime: "3 mins",
  },
  {
    title: "Lunch",
    description: "Delicious midday meals.",
    imageUrl: "https://images.unsplash.com/photo-1514512364185-4c2b678c5a68?auto=format&fit=crop&w=400&q=80",
    price: "$12.00",
    category: "Mains",
    rating: 4.9,
    prepTime: "10 mins",
  },
  {
    title: "Dinner",
    description: "Hearty dishes to end your day.",
    imageUrl: "https://images.unsplash.com/photo-1502741338009-cac2772e18bc?auto=format&fit=crop&w=400&q=80",
    price: "$15.00",
    category: "Mains",
    rating: 4.8,
    prepTime: "15 mins",
  },
  {
    title: "Brunch",
    description: "Perfect for late mornings.",
    imageUrl: "https://images.unsplash.com/photo-1464306076886-debca5e8a6b0?auto=format&fit=crop&w=400&q=80",
    price: "$10.00",
    category: "Brunch",
    rating: 4.6,
    prepTime: "8 mins",
  },
];

function FoodMenu() {
  const [meals, setMeals] = useState(initialMeals);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleAddMenuItem = (newItem: {
    title: string;
    description: string;
    imageUrl: string;
    price: string;
    category: string;
    rating: number;
    prepTime: string;
  }) => {
    setMeals((prev) => [...prev, newItem]);
  };

  return (
    <Screen>
      <header className="mb-6">
        <div className="flex-1">
          <div className="relative overflow-hidden bg-gradient-to-r from-orange-500 via-red-500 to-red-600 rounded-2xl shadow-2xl animate-pulse">
            <div className="px-8 py-12">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <div className="w-8 h-8 bg-white/30 rounded"></div>
                </div>
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold text-white drop-shadow">Food Menu</h1>
                  <div className="h-4 bg-white/20 rounded w-48"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <div className="flex justify-end mb-6">
        <PrimaryButton variant="secondary" onClick={() => setIsModalOpen(true)}>
          Add Menu Item
        </PrimaryButton>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {meals.map((meal) => (
          <MealCard
            key={meal.title}
            title={meal.title}
            description={meal.description}
            imageUrl={meal.imageUrl}
            price={meal.price}
            category={meal.category}
            rating={meal.rating}
            prepTime={meal.prepTime}
            onClick={() => {
              /* open modal logic here if needed */
            }}
            className="max-w-xs"
          />
        ))}
      </div>
      {isModalOpen && (
        <CreateMenuItemModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleAddMenuItem}
        />
      )}
    </Screen>
  );
}

export default FoodMenu;