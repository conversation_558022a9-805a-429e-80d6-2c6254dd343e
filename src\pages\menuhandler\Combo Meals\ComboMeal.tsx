import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import React, { useState } from "react";
import { CreateComboMealModal } from "./Modals/CreateCombo";
import { MealCard } from "./Components/ComboCard";



const initialMeals = [
  {
    title: "Burger, fries plus 500ml soda",
    description: "finger leaking good.",
    imageUrl: "https://i.pinimg.com/1200x/86/00/b1/8600b11df916a1d0275f49d8aa7d64c2.jpg",
    price: "kes8.50",
    category: " Lunch",
    TaxClass: "hgf",
    prepTime: "5 mins",
  },
  {
    title: "Sorte wings",
    description: "Refreshing drinks for every mood.",
    imageUrl: "https://i.pinimg.com/736x/e1/6c/6a/e16c6abdb9e04596239436e47178c1cc.jpg",
    price: "kes93.50",
    category: "Beverages",
    Taxclass: "jhgfd",
    prepTime: "3 mins",
  },
  {
    title: "Mbuzi mix",
    description: "the real delicoucy.",
    imageUrl: "https://i.pinimg.com/1200x/1c/b8/26/1cb826942ddef23e10a7e8d8ca87ac2e.jpg",
    price: "$12.00",
    category: "Mains",
    rating: 4.9,
    prepTime: "10 mins",
  },
  {
    title: "Dinner",
    description: "Hearty dishes to end your day.",
    imageUrl: "https://images.unsplash.com/photo-1502741338009-cac2772e18bc?auto=format&fit=crop&w=400&q=80",
    price: "$15.00",
    category: "Mains",
    rating: 4.8,
    prepTime: "15 mins",
  },
  {
    title: "Brunch",
    description: "Perfect for late mornings.",
    imageUrl: "https://images.unsplash.com/photo-1464306076886-debca5e8a6b0?auto=format&fit=crop&w=400&q=80",
    price: "$10.00",
    category: "Brunch",
    rating: 4.6,
    prepTime: "8 mins",
  },
];

function FoodCombo() {
  const [meals, setMeals] = useState(initialMeals);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleAddMenuItem = (newItem: {
    title: string;
    description: string;
    imageUrl: string;
    price: string;
    category: string;
    rating: number;
    prepTime: string;
  }) => {
    setMeals((prev) => [...prev, newItem]);
  };

  return (
    <Screen>
      <header className="mb-6">
        <div className="flex-1">
          <div className="relative overflow-hidden bg-gradient-to-r from-orange-500 via-red-500 to-red-600 rounded-2xl shadow-2xl animate-pulse">
            <div className="px-8 py-12">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <div className="w-8 h-8 bg-white/30 rounded"></div>
                </div>
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold text-white drop-shadow">Combo Menu</h1>
                  <div className="h-4 bg-white/20 rounded w-48"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <div className="flex justify-end mb-6">
        <PrimaryButton variant="secondary" onClick={() => setIsModalOpen(true)}>
          And Combo
        </PrimaryButton>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {meals.map((meal) => (
          <MealCard
            key={meal.title}
            title={meal.title}
            description={meal.description}
            imageUrl={meal.imageUrl}
            price={meal.price}
            category={meal.category}
            rating={meal.rating}
            prepTime={meal.prepTime}
            onClick={() => {
              /* open modal logic here if needed */
            }}
            className="max-w-xs"
          />
        ))}
      </div>
      {isModalOpen && (
        <CreateComboMealModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleAddMenuItem}
        />
      )}
    </Screen>
  );
}

export default FoodCombo;