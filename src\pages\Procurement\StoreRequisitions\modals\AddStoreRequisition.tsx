import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Trash2, Loader2 } from "lucide-react";
import {
  useCreateStoreRequisitionMutation,
  useGetCostCentersQuery,
  useGetStoresQuery,
  useGetProductsQuery,
  useGetUnitsOfMeasureQuery,
} from "@/redux/slices/procurement";
import { StoreRequisitionFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddStoreRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
}

const AddStoreRequisition = ({ isOpen, onClose }: AddStoreRequisitionProps) => {
  const [createStoreRequisition, { isLoading: creating }] = useCreateStoreRequisitionMutation();
  
  // Fetch supporting data
  const { data: costCenters } = useGetCostCentersQuery({});
  const { data: stores } = useGetStoresQuery({});
  const { data: products } = useGetProductsQuery({});
  const { data: unitsOfMeasure } = useGetUnitsOfMeasureQuery({});

  const [formData, setFormData] = useState<StoreRequisitionFormData>({
    cost_center: "",
    store: "",
    purpose: "",
    required_by: "",
    items: [
      {
        product: "",
        quantity: "",
        unit_of_measure: "",
        remarks: "",
      },
    ],
  });

  const handleInputChange = (field: keyof StoreRequisitionFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const addItem = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          product: "",
          quantity: "",
          unit_of_measure: "",
          remarks: "",
        },
      ],
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      setFormData((prev) => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index),
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.cost_center || !formData.store || !formData.purpose || !formData.required_by) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (formData.items.some(item => !item.product || !item.quantity || !item.unit_of_measure)) {
      toast.error("Please complete all item details");
      return;
    }

    try {
      const payload = {
        ...formData,
        items: formData.items.map(item => ({
          ...item,
          quantity: Number(item.quantity),
        })),
      };

      await createStoreRequisition(payload).unwrap();
      toast.success("Store requisition created successfully");
      onClose();
      
      // Reset form
      setFormData({
        cost_center: "",
        store: "",
        purpose: "",
        required_by: "",
        items: [
          {
            product: "",
            quantity: "",
            unit_of_measure: "",
            remarks: "",
          },
        ],
      });
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create store requisition");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Store Requisition</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="cost_center">Cost Center *</Label>
                <Select
                  value={formData.cost_center.toString()}
                  onValueChange={(value) => handleInputChange("cost_center", Number(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select cost center" />
                  </SelectTrigger>
                  <SelectContent>
                    {costCenters?.data?.results?.map((center: any) => (
                      <SelectItem key={center.id} value={center.id.toString()}>
                        {center.name} ({center.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="store">Store *</Label>
                <Select
                  value={formData.store.toString()}
                  onValueChange={(value) => handleInputChange("store", Number(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select store" />
                  </SelectTrigger>
                  <SelectContent>
                    {stores?.data?.results?.map((store: any) => (
                      <SelectItem key={store.id} value={store.id.toString()}>
                        {store.name} - {store.location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="required_by">Required By *</Label>
                <Input
                  id="required_by"
                  type="date"
                  value={formData.required_by}
                  onChange={(e) => handleInputChange("required_by", e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="purpose">Purpose *</Label>
                <Textarea
                  id="purpose"
                  value={formData.purpose}
                  onChange={(e) => handleInputChange("purpose", e.target.value)}
                  placeholder="Enter the purpose of this requisition..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">Items</CardTitle>
              <Button type="button" onClick={addItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    {formData.items.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Product *</Label>
                      <Select
                        value={item.product.toString()}
                        onValueChange={(value) => handleItemChange(index, "product", Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {products?.data?.results?.map((product: any) => (
                            <SelectItem key={product.id} value={product.id.toString()}>
                              {product.name} ({product.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Quantity *</Label>
                      <Input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, "quantity", e.target.value)}
                        placeholder="Enter quantity"
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Unit of Measure *</Label>
                      <Select
                        value={item.unit_of_measure.toString()}
                        onValueChange={(value) => handleItemChange(index, "unit_of_measure", Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {unitsOfMeasure?.data?.results?.map((unit: any) => (
                            <SelectItem key={unit.id} value={unit.id.toString()}>
                              {unit.name} ({unit.abbreviation})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Remarks</Label>
                    <Textarea
                      value={item.remarks}
                      onChange={(e) => handleItemChange(index, "remarks", e.target.value)}
                      placeholder="Any additional notes for this item..."
                      rows={2}
                    />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Requisition
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddStoreRequisition;
