import React, { useState } from "react";
// Import the modal component
import { Coffee, Utensils, Clock, Star } from "lucide-react";


// Define the Meal interface
export interface Meal {
  title: string;
  description: string;
  imageUrl: string;
  price?: string;
  category?: string;
  TaxClass?: number;
  prepTime?: string;
}



interface MealCardProps {
  title: string;
  description: string;
  imageUrl: string;
  price?: string;
  category?: string;
  rating?: number;
  prepTime?: string;
  onAddToCart?: (e?: React.MouseEvent<HTMLButtonElement>) => void;
  onViewDetails?: (e?: React.MouseEvent<HTMLButtonElement>) => void;
  onClick?: () => void;
  className?: string;
}

export function MealCard({
  title,
  description,
  imageUrl,
  price = "$8.50",
  category = "Mains",
  rating = 4.8,
  prepTime = "5 mins",
  onAddToCart,
  onViewDetails,
  onClick,
  className,
}: MealCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMeals, setModalMeals] = useState<Meal[]>([]);

  const handleOpenModal = (meals: Meal[]) => {
    setModalMeals(meals);
    setIsModalOpen(true);
  };

  const handleViewDetails = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsModalOpen(true);
    onViewDetails && onViewDetails(e);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div
        className={`
          group relative overflow-hidden
          bg-gradient-to-br from-white to-gray-50
          border-0 shadow-sm hover:shadow-xl
          transition-all duration-300 ease-out
          cursor-pointer
          backdrop-blur-sm
          hover:scale-[1.02] hover:-translate-y-1
          rounded-xl
          ${className || ""}
        `}
        onClick={onClick}
        tabIndex={0}
        role="button"
        aria-label={`Open ${title} details`}
      >
        {/* Gradient overlay for modern effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        <div className="relative flex items-center p-3 space-x-3">
          {/* Compact image */}
          <div className="relative flex-shrink-0 w-16 h-16 rounded-xl overflow-hidden">
            <img
              src={imageUrl}
              alt={title}
              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-black/10 group-hover:bg-black/0 transition-colors duration-300" />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 text-sm truncate group-hover:text-orange-600 transition-colors duration-200">
              {title}
            </h3>
            <p className="text-xs text-gray-500 mt-0.5 line-clamp-2 leading-relaxed">
              {description}
            </p>
          </div>

          {/* Action buttons */}
          <div className="flex-shrink-0 flex space-x-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onAddToCart && onAddToCart(e);
              }}
              disabled={!onAddToCart}
              className="
                px-3 py-1.5 text-xs font-medium
                bg-gradient-to-r from-orange-500 to-red-500
                text-white rounded-lg
                hover:from-orange-600 hover:to-red-600
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-all duration-200
                hover:shadow-lg hover:shadow-orange-500/25
                transform hover:scale-105
              "
              aria-label={`Add ${title} to cart`}
            >
              Add
            </button>

            <button
              onClick={handleViewDetails}
              className="
                px-3 py-1.5 text-xs font-medium
                bg-white border border-gray-200
                text-gray-700 rounded-lg
                hover:bg-gradient-to-r hover:from-yellow-50 hover:to-orange-50
                hover:border-orange-300
                transition-all duration-200
                hover:shadow-md
                transform hover:scale-105
              "
              aria-label={`View details for ${title}`}
            >
              View
            </button>
          </div>
        </div>

        {/* Bottom accent line */}
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>

      {/* Render Modal */}
      
      
    </>
  );
}