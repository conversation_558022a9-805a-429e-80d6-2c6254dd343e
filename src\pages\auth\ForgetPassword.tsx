import { Mail, Loader } from 'lucide-react';
import Logo from '@/assets/logo.png';
import { useForm } from 'react-hook-form';
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod";
import { Link } from 'react-router-dom';

const ForgotPasswordPage = () => {
  const formSchema = z.object({
    email: z.string().min(1, { message: "Email is required." }).email({ message: "Invalid email format." }),
  });

  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(formSchema),
  });

  const isLoading = false; // Set this to true when submitting

  const onSubmit = async (data) => {
    // handle submit logic
    console.log(data);
  };

  return (
    <div className="w-full min-h-screen lg:w-1/2 flex items-center justify-center bg-white px-4">
      <div className="w-full max-w-md flex flex-col items-center gap-8">
        {/* Logo */}
        <div className="flex justify-center mt-6 mb-2">
          <img src={Logo} alt="GMC Logo" className="h-16 w-auto" />
        </div>

        {/* Header */}
        <div className="text-center w-full">
          <h2 className="text-2xl font-bold text-gray-900 mb-1">Forgot Your Password?</h2>
          <p className="text-gray-500 text-sm mb-6">
            Don’t worry, happens to all of us.<br />
            Enter your email below to recover your password
          </p>
        </div>

        {/* Form */}
        <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="email">Email Address</label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
              <input
                {...register("email")}
                id="email"
                type="email"
                placeholder="Enter your email"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-gray-50 focus:outline-none focus:ring-2 focus:ring-red-500"
              />
            </div>
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <button
            className="w-full bg-black text-white font-semibold py-2 rounded-md hover:bg-gray-900 transition-colors mb-2"
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className='flex items-center justify-center gap-2'>
                <Loader className="animate-spin" size={22} />
                Recovering...
              </span>
            ) : (
              <span>Recover Password</span>
            )}
          </button>
        </form>

        {/* Back to Login Link */}
        <div className="text-center w-full">
          <Link to="/auth/login" className="text-gray-700 hover:underline text-sm font-medium">
            Back to Log In
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
