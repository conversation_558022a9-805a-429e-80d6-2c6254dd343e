import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Screen } from '@/app-components/layout/screen';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, Save, X, Printer, TestTube } from 'lucide-react';
import { PrinterFormData, PrinterType, PrinterInterfaceType } from '@/types/pos';
import {
  useCreatePrinterMutation,
  useUpdatePrinterMutation,
  useGetPrinterQuery
} from '@/redux/slices/printers';
import { useGetWorkstationsQuery } from '@/redux/slices/workstations';
import { useGetReceiptTemplatesQuery } from '@/redux/slices/receiptTemplates';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';
import { useEffect } from 'react';

interface PrinterFormProps {
  mode: 'create' | 'edit';
}

const PrinterForm: React.FC<PrinterFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();

  // API hooks
  const [createPrinter, { isLoading: isCreating }] = useCreatePrinterMutation();
  const [updatePrinter, { isLoading: isUpdating }] = useUpdatePrinterMutation();
  const { data: printerData, isLoading: isLoadingPrinter } = useGetPrinterQuery(id!, {
    skip: mode === 'create' || !id,
  });
  const { data: workstations = [], isLoading: loadingWorkstations } = useGetWorkstationsQuery({});
  const { data: receiptTemplates = [], isLoading: loadingTemplates } = useGetReceiptTemplatesQuery({});

  const form = useForm<PrinterFormData>({
    defaultValues: {
      name: '',
      ip_address: '',
      linked_workstation: '',
      printer_type: 'receipt' as any,
      interface_type: 'LAN' as any,
      location_note: '',
      is_backup: false,
      port: 9100,
      is_active: true,
      receipt_template: undefined
    },
  });

  // Load printer data for edit mode
  useEffect(() => {
    if (mode === 'edit' && printerData) {
      form.reset({
        name: printerData.name,
        ip_address: printerData.ip_address || '',
        linked_workstation: printerData.linked_printer || '',
        printer_type: printerData.printer_purpose || 'receipt',
        interface_type: printerData.interface_type || 'LAN',
        location_note: printerData.location_note || '',
        is_backup: printerData.is_backup_printer || false,
        port: printerData.port || 9100,
        is_active: printerData.is_active ?? true,
        receipt_template: printerData.receipt_template
      });
    }
  }, [printerData, form, mode]);

  const watchWorkstation = form.watch('linked_workstation');
  const watchInterfaceType = form.watch('interface_type');

  const onSubmit = async (data: PrinterFormData) => {
    try {
      // Validate required fields
      if (!data.name || !data.printer_type) {
        handleApiError({ message: 'Name and printer type are required fields' }, 'validate form');
        return;
      }

      const generatedCode = generatePrinterCode();
      if (!generatedCode) {
        handleApiError({ message: 'Failed to generate printer code. Please ensure name is provided.' }, 'generate printer code');
        return;
      }

      const printerPayload = {
        printer_code: generatedCode,
        name: data.name,
        ip_address: data.ip_address || '',
        port: data.port || 9100,
        is_active: data.is_active ?? true,
        printer_purpose: data.printer_type,
        interface_type: data.interface_type,
        location_note: data.location_note || '',
        is_backup_printer: data.is_backup || false,
        receipt_template: data.receipt_template
      };

      console.log('Submitting printer payload:', printerPayload);

      if (mode === 'create') {
        const result = await createPrinter(printerPayload).unwrap();
        console.log('Printer created successfully:', result);
        handleApiSuccess('Printer created successfully!', result);
      } else if (mode === 'edit' && id) {
        const result = await updatePrinter({ id, data: printerPayload }).unwrap();
        console.log('Printer updated successfully:', result);
        handleApiSuccess('Printer updated successfully!', result);
      }

      navigate('/pos/printers');
    } catch (error: any) {
      handleApiError(error, mode === 'create' ? 'create printer' : 'update printer');
    }
  };

  const generatePrinterCode = () => {
    const name = form.getValues('name');
    if (name && name.trim().length > 0) {
      const cleanName = name.trim().replace(/[^a-zA-Z]/g, '');
      const namePrefix = cleanName.length >= 3 ? cleanName.substring(0, 3) : cleanName.padEnd(3, 'X');
      const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      const code = 'PR' + namePrefix.toUpperCase() + randomSuffix;
      return code;
    }
    return '';
  };

  const testPrinter = async () => {
    // Simulate printer test
    console.log('Testing printer connection...');
    handleApiSuccess('Printer test completed successfully!');
  };

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => navigate('/pos/printers')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === 'create' ? 'Add Printer' : 'Edit Printer'}
          </h1>
          <p className="text-muted-foreground">
            {mode === 'create' 
              ? 'Configure a new thermal or receipt printer'
              : 'Update printer configuration and settings'
            }
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Enter the basic details for the printer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  rules={{ required: 'Printer name is required' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Printer Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Kitchen Printer 1, Receipt Printer" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="space-y-2">
                  <Label>Printer ID</Label>
                  <div className="flex space-x-2">
                    <Input 
                      value={generatePrinterId()} 
                      readOnly 
                      className="bg-muted"
                      placeholder="Auto-generated"
                    />
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => {
                        form.trigger('name');
                      }}
                    >
                      Generate
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    ID will be auto-generated based on printer name
                  </p>
                </div>
              </div>

              <FormField
                control={form.control}
                name="branchId"
                rules={{ required: 'Branch assignment is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assign to Branch *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select branch" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockBranches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {branch.name} ({branch.code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="revenueCenterId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assign to Revenue Center</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      disabled={!watchBranchId}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select revenue center (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {filteredRevenueCenters.map((rc) => (
                          <SelectItem key={rc.id} value={rc.id}>
                            {rc.name} ({rc.code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Optional: Assign to a specific revenue center within the branch
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="type"
                  rules={{ required: 'Printer type is required' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Printer Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select printer type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(PrinterType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="interfaceType"
                  rules={{ required: 'Interface type is required' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Interface Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select interface type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(PrinterInterfaceType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Network Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Network Configuration</CardTitle>
              <CardDescription>
                Configure network settings for the printer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {watchInterfaceType !== PrinterInterfaceType.BLUETOOTH && (
                  <FormField
                    control={form.control}
                    name="ipAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>IP Address</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="e.g., *************" 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Network IP address for LAN/Wi-Fi printers
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="deviceName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Device Name</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="e.g., EPSON-TM-T88V, STAR-TSP143" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Printer model or device identifier
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {mode === 'edit' && (
                <div className="flex items-center space-x-2">
                  <Button type="button" variant="outline" onClick={testPrinter}>
                    <TestTube className="h-4 w-4 mr-2" />
                    Test Printer
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    Test the printer connection and print a sample receipt
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Workstation Assignment */}
          <Card>
            <CardHeader>
              <CardTitle>Workstation Assignment</CardTitle>
              <CardDescription>
                Assign this printer to specific workstations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="workstationIds"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assigned Workstations</FormLabel>
                    <FormDescription>
                      Select workstations that can use this printer
                    </FormDescription>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                      {loadingWorkstations ? (
                        <div className="col-span-2 text-center text-muted-foreground">
                          Loading workstations...
                        </div>
                      ) : workstations.length === 0 ? (
                        <div className="col-span-2 text-center text-muted-foreground">
                          No workstations available
                        </div>
                      ) : (
                        workstations.map((workstation) => (
                          <div key={workstation.id} className="flex items-center space-x-2 p-3 border rounded-lg">
                            <Checkbox
                              id={workstation.id?.toString()}
                              checked={field.value?.includes(workstation.id?.toString()) || false}
                              onCheckedChange={(checked) => {
                                const currentValue = field.value || [];
                                const workstationId = workstation.id?.toString();
                                if (checked && workstationId) {
                                  field.onChange([...currentValue, workstationId]);
                                } else if (workstationId) {
                                  field.onChange(currentValue.filter(id => id !== workstationId));
                                }
                              }}
                            />
                            <Label htmlFor={workstation.id?.toString()} className="font-medium">
                              {workstation.name} ({workstation.Workstation_code})
                            </Label>
                          </div>
                        ))
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Print Routing */}
          <Card>
            <CardHeader>
              <CardTitle>Print Job Routing</CardTitle>
              <CardDescription>
                Configure which menu items and categories route to this printer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="menuItemRouting"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Menu Items</FormLabel>
                    <FormDescription>
                      Select specific menu items that should print to this printer
                    </FormDescription>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                      {menuItemOptions.map((item) => (
                        <div key={item} className="flex items-center space-x-2">
                          <Checkbox
                            id={`menu-${item}`}
                            checked={field.value.includes(item)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                field.onChange([...field.value, item]);
                              } else {
                                field.onChange(field.value.filter(i => i !== item));
                              }
                            }}
                          />
                          <Label htmlFor={`menu-${item}`} className="text-sm capitalize">
                            {item.replace('-', ' ')}
                          </Label>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="categoryRouting"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categories</FormLabel>
                    <FormDescription>
                      Select categories that should route to this printer
                    </FormDescription>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                      {categoryOptions.map((category) => (
                        <div key={category} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category}`}
                            checked={field.value.includes(category)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                field.onChange([...field.value, category]);
                              } else {
                                field.onChange(field.value.filter(c => c !== category));
                              }
                            }}
                          />
                          <Label htmlFor={`category-${category}`} className="text-sm capitalize">
                            {category.replace('-', ' ')}
                          </Label>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="backupPrinterId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Backup Printer</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select backup printer (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockPrinters.map((printer) => (
                          <SelectItem key={printer.id} value={printer.id}>
                            {printer.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Fallback printer if this printer becomes unavailable
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => navigate('/pos/printers')}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating || isUpdating || isLoadingPrinter}>
              <Save className="h-4 w-4 mr-2" />
              {(isCreating || isUpdating) ? 'Saving...' : mode === 'create' ? 'Create Printer' : 'Update Printer'}
            </Button>
          </div>
        </form>
      </Form>
      </div>
    </Screen>
  );
};

export default PrinterForm;
