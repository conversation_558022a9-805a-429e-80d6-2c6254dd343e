import { useState } from 'react';
import { <PERSON>, <PERSON>O<PERSON>, Loader } from 'lucide-react';
import Logo from '@/assets/logo.png';
import { useForm } from 'react-hook-form';
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod";
import { Link, useNavigate } from 'react-router-dom';
// import your signup mutation here if you have it, e.g. usePostSignupMutation

const SignupPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  // zod schema for signup validation
  const formSchema = z.object({
    fullName: z.string().min(2, { message: "Full name is required." }),
    email: z.string().email({ message: "Invalid email address." }),
    phone: z.string().min(8, { message: "Phone number is required." }),
    password: z.string().min(6, { message: "Password is required (min 6 chars)." }),
    confirmPassword: z.string().min(6, { message: "Please confirm your password." }),
  }).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

  const { register, handleSubmit, formState: { errors }, watch } = useForm({
    resolver: zodResolver(formSchema),
  });

  const navigate = useNavigate();

  // const [postSignup, { isLoading: isSignUpLoading }] = usePostSignupMutation();
  const isSignUpLoading = false; // Set to true when submitting

  const onSubmit = async (data) => {
    // try {
    //   await postSignup(data).unwrap();
    //   // show success toast
    //   navigate("/auth/login");
    // } catch (err) {
    //   // handle error toast
    // }
    console.log(data);
  };

  return (
    <div className="w-full min-h-screen lg:w-1/2 flex items-center justify-center bg-white px-4">
      <div className="w-full max-w-md flex flex-col items-center gap-8">
        {/* Logo */}
        <div className="flex justify-center mt-6 mb-2">
          <img src={Logo} alt="GMC Logo" className="h-16 w-auto" />
        </div>

        {/* Header */}
        <div className="text-center w-full">
          <h2 className="text-2xl font-bold text-gray-900 mb-1">Let’s Sign you up!</h2>
          <p className="text-gray-500 text-sm mb-6">
            Enter your details to create an account
          </p>
        </div>

        {/* Signup Form */}
        <form className="w-full" onSubmit={handleSubmit(onSubmit)}>

          {/* Full Names */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="fullName">Enter Full Names</label>
            <input
              {...register("fullName")}
              id="fullName"
              placeholder="Enter your name"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            />
            {errors.fullName && (
              <p className="text-red-500 text-xs mt-1">{errors.fullName.message}</p>
            )}
          </div>

          {/* Email */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="email">Email Address</label>
            <input
              {...register("email")}
              id="email"
              type="email"
              placeholder="Enter your email"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
            )}
          </div>

          {/* Phone Number */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="phone">Phone Number</label>
            <input
              {...register("phone")}
              id="phone"
              type="tel"
              placeholder="Enter your phone"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500"
            />
            {errors.phone && (
              <p className="text-red-500 text-xs mt-1">{errors.phone.message}</p>
            )}
          </div>

          {/* Password */}
          <div className="mb-4">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="password">Password</label>
            <div className="relative">
              <input
                {...register("password")}
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500 pr-10"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                tabIndex={-1}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">{errors.password.message}</p>
            )}
          </div>

          {/* Confirm Password */}
          <div className="mb-6">
            <label className="block text-sm text-gray-700 mb-1" htmlFor="confirmPassword">Confirm Password</label>
            <div className="relative">
              <input
                {...register("confirmPassword")}
                id="confirmPassword"
                type={showConfirm ? 'text' : 'password'}
                placeholder="Enter your password"
                className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500 pr-10"
              />
              <button
                type="button"
                onClick={() => setShowConfirm(!showConfirm)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                tabIndex={-1}
              >
                {showConfirm ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-red-500 text-xs mt-1">{errors.confirmPassword.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <button
            className="w-full bg-black text-white font-semibold py-2 rounded-md hover:bg-gray-900 transition-colors mb-2"
            type="submit"
            disabled={isSignUpLoading}
          >
            {isSignUpLoading ? (
              <span className='flex items-center justify-center gap-2'>
                <Loader className="animate-spin" size={22} />
                Signing Up...
              </span>
            ) : (
              <span>Sign In</span>
            )}
          </button>
        </form>

        {/* Sign In Link */}
        <div className="text-center w-full">
          <span className="text-gray-500 text-sm">Already have an account?</span>{" "}
          <Link to="/auth/login" className="text-red-500 hover:underline text-sm font-medium">
            Sign In
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SignupPage;
