import { apiSlice } from "../apiSlice";
import { logout, setCredentials, LoginRequest, LoginResponse, RegistrationRequest, LogoutRequest, RefreshTokenRequest, ResetLinkRequest } from "../authSlice";
import { User } from "../../types/user";

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    postLogin: builder.mutation<LoginResponse, LoginRequest>({
      query: (body) => ({
        url: "/users/login",
        method: "POST",
        body: body,
      }),
      // Automatically store the token when login is successful
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;
          dispatch(setCredentials(data));
        } catch (error) {
          console.log(error)
          // Handle error if needed
        }
      },
    }),
    postRegistration: builder.mutation<User, RegistrationRequest>({
      query: (body) => ({
        url: "/users/registration",
        method: "POST",
        body: body,
      }),
    }),
    postForgotPassword: builder.mutation<{ email: string }, ResetLinkRequest>({
      query: (body) => ({
        url: "/users/reset-link",
        method: "POST",
        body: body,
      }),
    }),
    postRefreshToken: builder.mutation<LoginResponse, RefreshTokenRequest>({
      query: (body) => ({
        url: "/users/refresh-token",
        method: "POST",
        body: body,
      }),
      // Automatically update credentials when refresh is successful
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;
          dispatch(setCredentials(data));
        } catch (error) {
          console.log(error);
          // If refresh fails, logout the user
          dispatch(logout());
        }
      },
    }),
    postLogout: builder.mutation<{ detail: string }, LogoutRequest>({
      query: (body) => ({
        url: '/users/logout',
        method: 'POST',
        body: body,
      }),
      // Clear credentials on logout
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
          dispatch(logout());
        } catch {
          dispatch(logout());
        }
      },
    }),
  }),
});

export const {
  usePostLoginMutation,
  usePostRegistrationMutation,
  usePostForgotPasswordMutation,
  usePostRefreshTokenMutation,
  usePostLogoutMutation,
} = authApiSlice;
