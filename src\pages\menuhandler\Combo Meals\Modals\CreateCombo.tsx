import React, { useState } from "react";
import { X, Utensils, Clock } from "lucide-react";

interface ComboMealItem {
  id: string;
  name: string;
  category: string;
  price: number;
  quantity: number;
}

interface CreateComboMealModalProps {
  onClose: () => void;
  onSave: (newCombo: {
    name: string;
    items: ComboMealItem[];
    ingredients: string;
    nutritionalValue: string;
    imageUrl: string;
    prepTime: string;
    totalPrice: number;
  }) => void;
}

// Mock data for available menu items (in a real app, this would come from an API or props)
const availableItems = [
  { id: "1", name: "Burger", category: "Main", price: 8.99 },
  { id: "2", name: "Fries", category: "Snack", price: 3.99 },
  { id: "3", name: "Soda", category: "Beverage", price: 1.99 },
  { id: "4", name: "Salad", category: "Side", price: 4.99 },
  { id: "5", name: "Chicken Wings", category: "Main", price: 9.99 },
];

export function CreateComboMealModal({ onClose, onSave }: CreateComboMealModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    items: [] as ComboMealItem[],
    ingredients: "",
    nutritionalValue: "",
    imageUrl: "",
    prepTime: "",
  });

  const [selectedItem, setSelectedItem] = useState("");
  const [quantity, setQuantity] = useState(1);

  // Calculate total price based on selected items and their quantities
  const totalPrice = formData.items.reduce((sum, item) => sum + item.price * item.quantity, 0);

  // Handle input changes for text fields
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle file input for image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setFormData((prev) => ({ ...prev, imageUrl }));
    }
  };

  // Handle adding an item to the combo
  const handleAddItem = () => {
    if (!selectedItem || quantity < 1) return;

    const item = availableItems.find((i) => i.id === selectedItem);
    if (item) {
      setFormData((prev) => ({
        ...prev,
        items: [
          ...prev.items,
          { id: item.id, name: item.name, category: item.category, price: item.price, quantity },
        ],
      }));
      setSelectedItem("");
      setQuantity(1);
    }
  };

  // Handle removing an item from the combo
  const handleRemoveItem = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((item) => item.id !== id),
    }));
  };

  // Handle quantity change for an existing item
  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item) =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      ),
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ ...formData, totalPrice });
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-y-auto animate-in zoom-in-95 slide-in-from-bottom-4 duration-300">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-full backdrop-blur-sm">
                <Utensils className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Create Combo Meal</h2>
                <p className="text-white/90 text-sm">Build your combo meal</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-full transition-colors duration-200"
            >
              <X className="h-6 w-6 text-white" />
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Combo Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
            />
          </div>

          {/* Item Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Add Items</label>
            <div className="flex space-x-2 mt-1">
              <select
                value={selectedItem}
                onChange={(e) => setSelectedItem(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
              >
                <option value="">Select an item</option>
                {availableItems.map((item) => (
                  <option key={item.id} value={item.id}>
                    {item.name} ({item.category}) - ${item.price.toFixed(2)}
                  </option>
                ))}
              </select>
              <input
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value))}
                min="1"
                className="w-20 rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
              />
              <button
                type="button"
                onClick={handleAddItem}
                className="px-3 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600"
              >
                Add
              </button>
            </div>
          </div>

          {/* Selected Items List */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Selected Items</label>
            {formData.items.length === 0 ? (
              <p className="text-gray-500 text-sm">No items added yet.</p>
            ) : (
              <ul className="mt-1 space-y-2">
                {formData.items.map((item) => (
                  <li key={item.id} className="flex justify-between items-center">
                    <span>
                      {item.name} ({item.category}) x {item.quantity} - ${(item.price * item.quantity).toFixed(2)}
                    </span>
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value))}
                        min="1"
                        className="w-16 rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                      />
                      <button
                        type="button"
                        onClick={() => handleRemoveItem(item.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        Remove
                      </button>
                    </div>
                  </li>
                ))}
              </ul>
            )}
            <p className="mt-2 font-medium">Total Price: ${totalPrice.toFixed(2)}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Ingredients</label>
            <textarea
              name="ingredients"
              value={formData.ingredients}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Nutritional Value</label>
            <textarea
              name="nutritionalValue"
              value={formData.nutritionalValue}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Image</label>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
            />
            {formData.imageUrl && (
              <img src={formData.imageUrl} alt="Preview" className="mt-2 h-20 w-20 object-cover rounded-md" />
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Preparation Time</label>
            <input
              type="text"
              name="prepTime"
              value={formData.prepTime}
              onChange={handleChange}
              required
              placeholder="e.g., 15 mins"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
            />
          </div>

          {/* Footer */}
          <div className="sticky bottom-0 bg-gradient-to-r from-gray-50 to-white border-t border-gray-200 p-4">
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-white border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-6 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium rounded-lg hover:from-orange-600 hover:to-red-600 transition-all duration-200 hover:shadow-md transform hover:scale-105"
                disabled={formData.items.length === 0}
              >
                Save Combo
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}