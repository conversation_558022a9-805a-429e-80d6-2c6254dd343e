import React from "react";

interface Meal {
  title: string;
  description: string;
  imageUrl: string;
  price?: string;
  category?: string;
  rating?: number;
  prepTime?: string;
}

interface MealModalProps {
  meals: Meal[];
  onClose: () => void;
}

export function MealModal({ meals, onClose }: MealModalProps) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"
        onClick={onClose}
      />
      {/* Modal Content */}
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden animate-in zoom-in-95 slide-in-from-bottom-4 duration-300">
        <div className="sticky top-0 z-10 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 p-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">Meals</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-full transition-colors duration-200"
            >
              ✕
            </button>
          </div>
        </div>
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-100px)]">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {meals.map((meal, idx) => (
              <div
                key={idx}
                className="bg-gradient-to-br from-white to-gray-50 rounded-xl shadow p-4 flex flex-col"
              >
                <img
                  src={meal.imageUrl}
                  alt={meal.title}
                  className="object-cover w-full h-24 rounded mb-3"
                />
                <h3 className="text-lg font-semibold">{meal.title}</h3>
                <p className="text-gray-600 text-sm mb-2">{meal.description}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{meal.category}</span>
                  <span>
                    ⭐ {meal.rating}
                  </span>
                  <span>
                    ⏱ {meal.prepTime}
                  </span>
                </div>
                <div className="mt-2 text-orange-600 font-bold">{meal.price}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}