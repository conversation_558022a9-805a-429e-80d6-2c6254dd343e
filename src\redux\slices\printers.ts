import { apiSlice } from "../apiSlice";

// Types for Printer API
export interface Printer {
  id?: number;
  printer_code: string;
  name: string;
  ip_address?: string;
  port?: number;
  is_active?: boolean;
  printer_purpose?: 'receipt' | 'bill' | 'kitchen' | 'bar';
  interface_type?: 'LAN' | 'IP' | 'WIFI' | 'Bluetooth';
  location_note?: string;
  is_backup_printer?: boolean;
  receipt_template?: number;
}

export const printerApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all printers
    getPrinters: builder.query<Printer[], any>({
      query: (params) => ({
        url: "/setup/printers",
        method: "GET",
        params: params,
      }),
      providesTags: ["Printers"],
    }),

    // Get single printer
    getPrinter: builder.query<Printer, string>({
      query: (id) => ({
        url: `/setup/printers/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Printers", id }],
    }),

    // Create printer
    createPrinter: builder.mutation<Printer, Partial<Printer>>({
      query: (payload) => ({
        url: "/setup/printers",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Printers"],
    }),

    // Update printer
    updatePrinter: builder.mutation<Printer, { id: string; data: Partial<Printer> }>({
      query: ({ id, data }) => ({
        url: `/setup/printers/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Printers", id }, "Printers"],
    }),

    // Delete printer
    deletePrinter: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/printers/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Printers"],
    }),
  }),
});

export const { 
  useGetPrintersQuery, 
  useGetPrinterQuery,
  useCreatePrinterMutation,
  useUpdatePrinterMutation,
  useDeletePrinterMutation,
} = printerApiSlice;
