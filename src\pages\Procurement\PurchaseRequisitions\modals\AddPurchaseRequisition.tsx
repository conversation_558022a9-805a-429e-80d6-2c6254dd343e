import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Trash2, Loader2, AlertTriangle } from "lucide-react";
import {
  useCreatePurchaseRequisitionMutation,
  useGetCostCentersQuery,
  useGetProductsQuery,
  useGetUnitsOfMeasureQuery,
  useGetProcurementOfficersQuery,
  useGetSupplierCategoriesQuery,
  useGetDepartmentsQuery,
} from "@/redux/slices/procurement";
import { PurchaseRequisitionFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddPurchaseRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
}

const AddPurchaseRequisition = ({ isOpen, onClose }: AddPurchaseRequisitionProps) => {
  const [createPurchaseRequisition, { isLoading: creating }] = useCreatePurchaseRequisitionMutation();
  
  // Fetch supporting data
  const { data: costCenters } = useGetCostCentersQuery({});
  const { data: products } = useGetProductsQuery({});
  const { data: unitsOfMeasure } = useGetUnitsOfMeasureQuery({});
  const { data: procurementOfficers } = useGetProcurementOfficersQuery({});
  const { data: supplierCategories } = useGetSupplierCategoriesQuery({});
  const { data: departments } = useGetDepartmentsQuery({});

  const [formData, setFormData] = useState<PurchaseRequisitionFormData>({
    store_requisition: "",
    assigned_to: "",
    supplier_category: "",
    budget_code: "",
    required_date: "",
    priority: "",
    items: [
      {
        product: "",
        quantity: "",
        unit_of_measure: "",
        estimated_unit_cost: "",
        specifications: "",
        preferred_supplier: "",
        justification: "",
      },
    ],
  });

  const [selectedDepartment, setSelectedDepartment] = useState("");
  const [selectedCostCenter, setSelectedCostCenter] = useState("");

  const handleInputChange = (field: keyof PurchaseRequisitionFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const addItem = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          product: "",
          quantity: "",
          unit_of_measure: "",
          estimated_unit_cost: "",
          specifications: "",
          preferred_supplier: "",
          justification: "",
        },
      ],
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      setFormData((prev) => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index),
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.supplier_category || !formData.budget_code || !formData.required_date || !formData.priority) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (formData.items.some(item => !item.product || !item.quantity || !item.unit_of_measure || !item.estimated_unit_cost)) {
      toast.error("Please complete all item details");
      return;
    }

    try {
      const payload = {
        ...formData,
        department: selectedDepartment,
        cost_center: selectedCostCenter ? Number(selectedCostCenter) : undefined,
        items: formData.items.map(item => ({
          ...item,
          product: Number(item.product),
          quantity: Number(item.quantity),
          unit_of_measure: Number(item.unit_of_measure),
          estimated_unit_cost: Number(item.estimated_unit_cost),
        })),
      };

      await createPurchaseRequisition(payload).unwrap();
      toast.success("Purchase requisition created successfully");
      onClose();
      
      // Reset form
      setFormData({
        store_requisition: "",
        assigned_to: "",
        supplier_category: "",
        budget_code: "",
        required_date: "",
        priority: "",
        items: [
          {
            product: "",
            quantity: "",
            unit_of_measure: "",
            estimated_unit_cost: "",
            specifications: "",
            preferred_supplier: "",
            justification: "",
          },
        ],
      });
      setSelectedDepartment("");
      setSelectedCostCenter("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create purchase requisition");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Purchase Requisition</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="department">Department *</Label>
                <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments?.data?.results?.map((dept: any) => (
                      <SelectItem key={dept.id} value={dept.name}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cost_center">Cost Center</Label>
                <Select value={selectedCostCenter} onValueChange={setSelectedCostCenter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select cost center" />
                  </SelectTrigger>
                  <SelectContent>
                    {costCenters?.data?.results?.map((center: any) => (
                      <SelectItem key={center.id} value={center.id.toString()}>
                        {center.name} ({center.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="assigned_to">Assign to Procurement Officer</Label>
                <Select
                  value={formData.assigned_to.toString()}
                  onValueChange={(value) => handleInputChange("assigned_to", Number(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select procurement officer" />
                  </SelectTrigger>
                  <SelectContent>
                    {procurementOfficers?.data?.results?.map((officer: any) => (
                      <SelectItem key={officer.id} value={officer.id.toString()}>
                        {officer.name} - {officer.department}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="supplier_category">Supplier Category *</Label>
                <Select
                  value={formData.supplier_category}
                  onValueChange={(value) => handleInputChange("supplier_category", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier category" />
                  </SelectTrigger>
                  <SelectContent>
                    {supplierCategories?.data?.results?.map((category: any) => (
                      <SelectItem key={category.id} value={category.name}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="budget_code">Budget Code *</Label>
                <Input
                  id="budget_code"
                  value={formData.budget_code}
                  onChange={(e) => handleInputChange("budget_code", e.target.value)}
                  placeholder="Enter budget code"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="required_date">Required Date *</Label>
                <Input
                  id="required_date"
                  type="date"
                  value={formData.required_date}
                  onChange={(e) => handleInputChange("required_date", e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority *</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => handleInputChange("priority", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                        High
                      </div>
                    </SelectItem>
                    <SelectItem value="Urgent">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        Urgent
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">Items</CardTitle>
              <Button type="button" onClick={addItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    {formData.items.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Product *</Label>
                      <Select
                        value={item.product.toString()}
                        onValueChange={(value) => handleItemChange(index, "product", Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {products?.data?.results?.map((product: any) => (
                            <SelectItem key={product.id} value={product.id.toString()}>
                              {product.name} ({product.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Quantity *</Label>
                      <Input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, "quantity", e.target.value)}
                        placeholder="Enter quantity"
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Unit of Measure *</Label>
                      <Select
                        value={item.unit_of_measure.toString()}
                        onValueChange={(value) => handleItemChange(index, "unit_of_measure", Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {unitsOfMeasure?.data?.results?.map((unit: any) => (
                            <SelectItem key={unit.id} value={unit.id.toString()}>
                              {unit.name} ({unit.abbreviation})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Estimated Unit Cost *</Label>
                      <Input
                        type="number"
                        value={item.estimated_unit_cost}
                        onChange={(e) => handleItemChange(index, "estimated_unit_cost", e.target.value)}
                        placeholder="Enter estimated cost per unit"
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Preferred Supplier</Label>
                      <Input
                        value={item.preferred_supplier}
                        onChange={(e) => handleItemChange(index, "preferred_supplier", e.target.value)}
                        placeholder="Enter preferred supplier (optional)"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Specifications</Label>
                    <Textarea
                      value={item.specifications}
                      onChange={(e) => handleItemChange(index, "specifications", e.target.value)}
                      placeholder="Enter detailed specifications..."
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Justification</Label>
                    <Textarea
                      value={item.justification}
                      onChange={(e) => handleItemChange(index, "justification", e.target.value)}
                      placeholder="Justify why this item is needed..."
                      rows={2}
                    />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Purchase Requisition
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddPurchaseRequisition;
