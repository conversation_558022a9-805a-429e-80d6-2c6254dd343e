import { apiSlice } from "../apiSlice";
import { 
  User, 
  GETUser, 
  CreateUserRequest, 
  UpdateUserRequest, 
  UsersQueryParams, 
  PaginatedUsersResponse 
} from "../../types/user";

export const userApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all users with pagination and search
    getUsers: builder.query<PaginatedUsersResponse, UsersQueryParams>({
      query: (params = {}) => ({
        url: "/users/users",
        method: "GET",
        params: params,
      }),
      providesTags: ["Users"],
    }),

    // Get single user by ID
    getUser: builder.query<GETUser, number>({
      query: (id) => ({
        url: `/users/users/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Users", id }],
    }),

    // Create new user
    createUser: builder.mutation<User, CreateUserRequest>({
      query: (payload) => ({
        url: "/users/registration",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Users"],
    }),

    // Update user
    updateUser: builder.mutation<User, { id: number; data: UpdateUserRequest }>({
      query: ({ id, data }) => ({
        url: `/users/users/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Users", id }, "Users"],
    }),

    // Delete user
    deleteUser: builder.mutation<void, number>({
      query: (id) => ({
        url: `/users/users/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Users"],
    }),

    // Health check endpoint
    healthCheck: builder.query<any, void>({
      query: () => ({
        url: "/sys/healthz",
        method: "GET",
      }),
    }),
  }),
});

export const {
  useGetUsersQuery,
  useGetUserQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useHealthCheckQuery,
  useLazyGetUsersQuery,
  useLazyGetUserQuery,
  useLazyHealthCheckQuery,
} = userApiSlice;
