import {
  BaseQuery<PERSON>pi,
  create<PERSON><PERSON>,
  Fetch<PERSON>rgs,
  fetchBaseQuery,
} from "@reduxjs/toolkit/query/react";
import { RootState } from "./store";
import { logout } from "./authSlice";

const environment = import.meta.env.VITE_PROD;

const url =
  environment == "production"
    ? import.meta.env.VITE_API_URL_PROD
    : import.meta.env.VITE_API_URL_DEV;

// Debug: Log the URL being used
console.log("Environment:", environment);
console.log("API URL:", url);

const baseQueryWithReauth = async (
  args: string | FetchArgs,
  api: BaseQueryApi,
  extraOptions: Record<string, any>
) => {
  // Debug: Log the API call details
  console.log("API Call:", {
    args,
    endpoint: api.endpoint,
    baseUrl: url,
  });

  const baseQuery = fetchBaseQuery({
    baseUrl: url,
    prepareHeaders: (headers, { getState, endpoint }) => {
      const publicEndpoints = ["postLogin", "postForgotPassword"];
      const isPublicEndpoint = publicEndpoints.some((ep) =>
        endpoint.includes(ep)
      );

      if (!isPublicEndpoint) {
        const token = (getState() as RootState).auth.token?.AccessToken;
        if (token) {
          headers.set("Authorization", `Token ${token}`);
          console.log("Added auth token to request");
        } else {
          console.log("No auth token found");
        }
      }
      return headers;
    },
  });

  const result = await baseQuery(args, api, extraOptions);

  // Debug: Log the API response
  console.log("API Response:", {
    endpoint: api.endpoint,
    result,
    error: result.error,
    data: result.data,
  });

  // 401 logout
  if (result?.error?.status === 401) {
    console.log("401 Unauthorized - logging out");
    api.dispatch(logout());
  }

  return result;
};

// Define a service using a base URL and expected endpoints
export const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),

  tagTypes: [
    "Auth",
    "Permissions",
    "Users",
    "Branches",
    "CostCenters",
    "Printers",
    "ReceiptTemplates",
    "RevenueCenters",
    "Stores",
    "SupplierBankDetails",
    "SupplierCategories",
    "Suppliers",
    "TaxClasses",
    "TaxRates",
    "UnitsOfMeasure",
    "Workstations",
    "UserRoles",
    "StoreRequisitions",
    "PurchaseRequisitions",
    "RFQs",
    "RFQResponses",
    "BidAnalyses",
    "PurchaseOrders",
    "GRNs",
    "GuestCheck",
    "ComboMenu",
    "ComboComponent",
  ],
});
