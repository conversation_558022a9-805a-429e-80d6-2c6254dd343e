import { apiSlice } from "../apiSlice";

export const UnitOfMeasureApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getUnitOfMeasure: builder.query({
      query: (params) => ({
        url: "/UnitOfMeasure",
        method: "GET",
        params: params,
      }),
    }),

    retrieveUnitOfMeasure: builder.query({
      query: (id) => ({
        url: `/UnitOfMeasure/${id}`,
        method: "GET",
      }),
    }),

    addUnitOfMeasure: builder.mutation({
      query: (payload) => ({
        url: "/UnitOfMeasure",
        method: "POST",
        body: payload,
      }),
    }),

    patchUnitOfMeasure: builder.mutation({
      query: (payload) => ({
        url: `/UnitOfMeasure/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
    }),
  }),
});

export const {
  useGetUnitOfMeasureQuery,
  useRetrieveUnitOfMeasureQuery,
  useAddUnitOfMeasureMutation,
  usePatchUnitOfMeasureMutation,

  useLazyGetUnitOfMeasureQuery,
  useLazyRetrieveUnitOfMeasureQuery,
} = UnitOfMeasureApiSlice;
