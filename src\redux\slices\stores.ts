import { apiSlice } from "../apiSlice";

// Types for Store API
export interface Store {
  id?: number;
  name: string;
  code: string;
  is_active?: boolean;
  location?: string;
  branch: string;
}

export const storeApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all stores
    getStores: builder.query<Store[], any>({
      query: (params) => ({
        url: "/setup/stores",
        method: "GET",
        params: params,
      }),
      providesTags: ["Stores"],
    }),

    // Get single store
    getStore: builder.query<Store, string>({
      query: (id) => ({
        url: `/setup/stores/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Stores", id }],
    }),

    // Create store
    createStore: builder.mutation<Store, Partial<Store>>({
      query: (payload) => ({
        url: "/setup/stores",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Stores"],
    }),

    // Update store
    updateStore: builder.mutation<Store, { id: string; data: Partial<Store> }>({
      query: ({ id, data }) => ({
        url: `/setup/stores/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Stores", id }, "Stores"],
    }),

    // Delete store
    deleteStore: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/stores/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Stores"],
    }),
  }),
});

export const { 
  useGetStoresQuery, 
  useGetStoreQuery,
  useCreateStoreMutation,
  useUpdateStoreMutation,
  useDeleteStoreMutation,
} = storeApiSlice;
