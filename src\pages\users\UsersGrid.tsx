import { useState, useMemo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { OutlinedButton } from "@/components/custom/buttons/buttons";
import { Badge } from "@/components/custom/badges/badges";
import { Card6 } from "@/components/custom/cards/Card6";
import UserCard from "./UserCard";
import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import {
  Plus,
  Search,
  Users,
  UserCheck,
  Building,
  Filter,
  Grid3X3,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { useGetUsersQuery } from "@/redux/slices/user";
import { Link } from "react-router-dom";

export type UserStatus = "active" | "inactive";

export interface User {
  id?: number | null;
  employee_no: string | null;
  email: string | null;
  fullnames: string | null;
  company_email?: string | null;
  gender?: string | null;
  department?: string | null;
  designation?: string | null;
  status?: UserStatus | null;
  team?: string | null;
  region?: string | null;
  manager?: string | null;
  phone_number?: string | null;
}

export default function UsersGrid() {
  // Fetch all users with high page size to get all records
  const { data: usersList, isLoading, isError, refetch } = useGetUsersQuery({
    page_size: 1000, // Fetch up to 1000 users
    ordering: "-id", // Order by newest first
  });

  const [searchTerm, setSearchTerm] = useState("");
  const [itemsPerPage, setItemsPerPage] = useState("9");
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState("all");

  // Map the fetched data to the grid format
  const users: User[] = useMemo(() => {
    // Handle paginated response structure
    const results = usersList?.results || [];
    return Array.isArray(results)
      ? results.map((user: any) => ({
          id: user.id ?? null,
          employee_no: user.employee_no ?? null,
          email: user.email ?? null,
          fullnames: user.fullnames || (user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : null),
          company_email: user.company_email ?? null,
          gender: user.gender ?? null,
          department: user.department ?? null,
          designation: user.designation ?? null,
          status: user.status ?? null,
          team: user.team ?? null,
          region: user.region ?? null,
          manager: user.manager ?? null,
          phone_number: user.phone || user.phone_number ?? null,
        }))
      : [];
  }, [usersList]);

  // Filter users based on search term and status
  const filteredUsers = useMemo(() => {
    let filtered = users;

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter((user) =>
        (user.fullnames?.toLowerCase() || "").includes(searchLower) ||
        (user.email?.toLowerCase() || "").includes(searchLower) ||
        (user.employee_no?.toLowerCase() || "").includes(searchLower) ||
        (user.department?.toLowerCase() || "").includes(searchLower) ||
        (user.designation?.toLowerCase() || "").includes(searchLower) ||
        (user.team?.toLowerCase() || "").includes(searchLower) ||
        (user.region?.toLowerCase() || "").includes(searchLower) ||
        (user.manager?.toLowerCase() || "").includes(searchLower) ||
        (user.phone_number?.toLowerCase() || "").includes(searchLower)
      );
    }

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((user) =>
        user.status?.toLowerCase() === statusFilter.toLowerCase()
      );
    }

    return filtered;
  }, [users, searchTerm, statusFilter]);

  // Pagination logic
  const itemsPerPageNumber = Number(itemsPerPage) || 10;
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPageNumber);
  const startIndex = (currentPage - 1) * itemsPerPageNumber;
  const endIndex = startIndex + itemsPerPageNumber;
  const currentUsers = filteredUsers.slice(startIndex, endIndex);

  // Calculate stats for Card6 components
  const totalUsers = users.length;
  const activeUsers = users.filter(u => u.status?.toLowerCase() === 'active').length;
  const departments = new Set(users.map(u => u.department).filter(d => d && d !== 'N/A')).size;

  console.log("user:", usersList);

  if (isLoading) {
    return (
      <Screen>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Users Grid</h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">Browse and manage users in grid view</p>
            </div>
          </div>

          {/* Loading Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 animate-pulse">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 mb-2"></div>
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                  </div>
                  <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                </div>
              </div>
            ))}
          </div>

          {/* Loading Grid */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600 dark:text-gray-400">Loading users...</p>
              </div>
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  if (isError) {
    return (
      <Screen>
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Users Grid</h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">Browse and manage users in grid view</p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12">
            <div className="text-center">
              <div className="text-red-500 text-lg font-medium">Failed to load users</div>
              <p className="text-gray-600 dark:text-gray-400 mt-2">Please try again later.</p>
              <OutlinedButton onClick={() => refetch()} className="mt-4">
                Retry
              </OutlinedButton>
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Users Grid</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">Browse and manage users in grid view</p>
          </div>
          
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card6
            title="Total Users"
            value={totalUsers}
            icon={Users}
            iconBg="bg-gradient-to-r from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30"
            iconColor="text-blue-600 dark:text-blue-400"
            change={`+${activeUsers}`}
            changeLabel="Active Users"
            positive={true}
            cardBg="bg-gradient-to-r from-white to-blue-50 dark:from-gray-800 dark:to-blue-900/20"
          />
          <Card6
            title="Active Users"
            value={activeUsers}
            icon={UserCheck}
            iconBg="bg-gradient-to-r from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/30"
            iconColor="text-green-600 dark:text-green-400"
            change={`${totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0}%`}
            changeLabel="of total users"
            positive={true}
            cardBg="bg-gradient-to-r from-white to-green-50 dark:from-gray-800 dark:to-green-900/20"
          />
          <Card6
            title="Departments"
            value={departments}
            icon={Building}
            iconBg="bg-gradient-to-r from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30"
            iconColor="text-purple-600 dark:text-purple-400"
            change={`${users.filter(u => u.department && u.department !== 'N/A').length}`}
            changeLabel="users assigned"
            positive={true}
            cardBg="bg-gradient-to-r from-white to-purple-50 dark:from-gray-800 dark:to-purple-900/20"
          />
        </div>

        {/* Filters and Controls */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
              <div className="flex items-center gap-2">
                <Grid3X3 className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Show</span>
                <Select value={itemsPerPage} onValueChange={setItemsPerPage}>
                  <SelectTrigger className="w-20">
                    <SelectValue placeholder="9" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="9">9</SelectItem>
                    <SelectItem value="18">18</SelectItem>
                    <SelectItem value="36">36</SelectItem>
                    <SelectItem value="72">72</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-gray-500">per page</span>
              </div>

              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Status</span>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="search"
                  placeholder="Search users..."
                  className="pl-10 w-64 flex h-10 rounded-md border border-gray bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray focus-visible:outline-none focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Results Summary */}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
              <span>
                Showing {startIndex + 1} to {Math.min(endIndex, filteredUsers.length)} of{" "}
                {filteredUsers.length} users
                {searchTerm && ` (filtered from ${totalUsers} total)`}
              </span>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {filteredUsers.length} results
                </Badge>
              </div>
            </div>
          </div>
        </div>
        {/* Users Grid */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          {currentUsers.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <div className="text-gray-500 text-lg font-medium">
                {searchTerm || statusFilter !== "all" ? "No users found matching your criteria" : "No users found"}
              </div>
              <p className="text-gray-400 mt-2">
                {searchTerm || statusFilter !== "all"
                  ? "Try adjusting your search terms or filters"
                  : "Users will appear here once they are added to the system"
                }
              </p>
              {(searchTerm || statusFilter !== "all") && (
                <OutlinedButton
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("");
                    setStatusFilter("all");
                  }}
                  className="mt-4"
                >
                  Clear Filters
                </OutlinedButton>
              )}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-4 md:gap-6">
                {currentUsers.map((user) => (
                  <Link
                    key={user.id}
                    to={`/profile/${user.id}`}
                    className="transform hover:scale-105 transition-all duration-200 h-full"
                  >
                    <div className="h-full">
                      <UserCard user={user} />
                    </div>
                  </Link>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Page {currentPage} of {totalPages}
                    </div>
                    <div className="flex items-center gap-2">
                      <OutlinedButton
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                        disabled={currentPage === 1}
                        className="flex items-center gap-1"
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </OutlinedButton>

                      <div className="flex gap-1">
                        {[...Array(Math.min(5, totalPages))].map((_, i) => {
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }

                          return (
                            <OutlinedButton
                              key={pageNum}
                              variant={currentPage === pageNum ? "primary" : "outline"}
                              size="sm"
                              onClick={() => setCurrentPage(pageNum)}
                              className="w-10"
                            >
                              {pageNum}
                            </OutlinedButton>
                          );
                        })}
                      </div>

                      <OutlinedButton
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-1"
                      >
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </OutlinedButton>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Screen>
  );
}