import { apiSlice } from "../apiSlice";

export const procurementApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Store Requisitions
    getStoreRequisitions: builder.query({
      query: (params) => ({
        url: "/store-requisitions",
        method: "GET",
        params: params,
      }),
      providesTags: ["StoreRequisitions"],
    }),

    getStoreRequisition: builder.query({
      query: (id) => ({
        url: `/store-requisitions/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "StoreRequisitions", id }],
    }),

    createStoreRequisition: builder.mutation({
      query: (payload) => ({
        url: "/store-requisitions",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["StoreRequisitions"],
    }),

    updateStoreRequisition: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/store-requisitions/${id}`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
    }),

    deleteStoreRequisition: builder.mutation({
      query: (id) => ({
        url: `/store-requisitions/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["StoreRequisitions"],
    }),

    submitStoreRequisition: builder.mutation({
      query: (id) => ({
        url: `/store-requisitions/${id}/submit`,
        method: "POST",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
    }),

    approveStoreRequisition: builder.mutation({
      query: (id) => ({
        url: `/store-requisitions/${id}/approve`,
        method: "POST",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
    }),

    rejectStoreRequisition: builder.mutation({
      query: ({ id, reason }) => ({
        url: `/store-requisitions/${id}/reject`,
        method: "POST",
        body: { reason },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
    }),

    convertToPurchaseRequisition: builder.mutation({
      query: (id) => ({
        url: `/store-requisitions/${id}/convert-to-purchase`,
        method: "POST",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
        "PurchaseRequisitions",
      ],
    }),

    // Purchase Requisitions
    getPurchaseRequisitions: builder.query({
      query: (params) => ({
        url: "/purchase-requisitions",
        method: "GET",
        params: params,
      }),
      providesTags: ["PurchaseRequisitions"],
    }),

    getPurchaseRequisition: builder.query({
      query: (id) => ({
        url: `/purchase-requisitions/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "PurchaseRequisitions", id }],
    }),

    createPurchaseRequisition: builder.mutation({
      query: (payload) => ({
        url: "/purchase-requisitions",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["PurchaseRequisitions"],
    }),

    updatePurchaseRequisition: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/purchase-requisitions/${id}`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
    }),

    deletePurchaseRequisition: builder.mutation({
      query: (id) => ({
        url: `/purchase-requisitions/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["PurchaseRequisitions"],
    }),

    submitPurchaseRequisition: builder.mutation({
      query: (id) => ({
        url: `/purchase-requisitions/${id}/submit`,
        method: "POST",
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
    }),

    approvePurchaseRequisition: builder.mutation({
      query: (id) => ({
        url: `/purchase-requisitions/${id}/approve`,
        method: "POST",
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
    }),

    rejectPurchaseRequisition: builder.mutation({
      query: ({ id, reason }) => ({
        url: `/purchase-requisitions/${id}/reject`,
        method: "POST",
        body: { reason },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
    }),

    assignPurchaseRequisition: builder.mutation({
      query: ({ id, assigned_to }) => ({
        url: `/purchase-requisitions/${id}/assign`,
        method: "POST",
        body: { assigned_to },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
    }),

    convertToRFQ: builder.mutation({
      query: (id) => ({
        url: `/purchase-requisitions/${id}/convert-to-rfq`,
        method: "POST",
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
        "RFQs",
      ],
    }),

    // Purchase Orders
    getPurchaseOrders: builder.query({
      query: (params) => ({
        url: "/purchase-orders",
        method: "GET",
        params: params,
      }),
      providesTags: ["PurchaseOrders"],
    }),

    getPurchaseOrder: builder.query({
      query: (id) => ({
        url: `/purchase-orders/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "PurchaseOrders", id }],
    }),

    createPurchaseOrder: builder.mutation({
      query: (payload) => ({
        url: "/purchase-orders",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["PurchaseOrders"],
    }),

    updatePurchaseOrder: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/purchase-orders/${id}`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "PurchaseOrders", id },
        "PurchaseOrders",
      ],
    }),

    deletePurchaseOrder: builder.mutation({
      query: (id) => ({
        url: `/purchase-orders/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["PurchaseOrders"],
    }),

    submitPurchaseOrder: builder.mutation({
      query: (id) => ({
        url: `/purchase-orders/${id}/submit`,
        method: "POST",
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: "PurchaseOrders", id },
        "PurchaseOrders",
      ],
    }),

    approvePurchaseOrder: builder.mutation({
      query: (id) => ({
        url: `/purchase-orders/${id}/approve`,
        method: "POST",
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: "PurchaseOrders", id },
        "PurchaseOrders",
      ],
    }),

    rejectPurchaseOrder: builder.mutation({
      query: ({ id, reason }) => ({
        url: `/purchase-orders/${id}/reject`,
        method: "POST",
        body: { reason },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "PurchaseOrders", id },
        "PurchaseOrders",
      ],
    }),

    sendPurchaseOrderEmail: builder.mutation({
      query: ({ id, email_data }) => ({
        url: `/purchase-orders/${id}/send-email`,
        method: "POST",
        body: email_data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "PurchaseOrders", id },
        "PurchaseOrders",
      ],
    }),

    exportPurchaseOrderPDF: builder.mutation({
      query: (id) => ({
        url: `/purchase-orders/${id}/export-pdf`,
        method: "POST",
        responseHandler: (response) => response.blob(),
      }),
    }),

    // RFQs
    getRFQs: builder.query({
      query: (params) => ({
        url: "/rfqs",
        method: "GET",
        params: params,
      }),
      providesTags: ["RFQs"],
    }),

    getRFQ: builder.query({
      query: (id) => ({
        url: `/rfqs/${id}`,
        method: "GET",
      }),
      providesTags: (_result, _error, id) => [{ type: "RFQs", id }],
    }),

    createRFQ: builder.mutation({
      query: (payload) => ({
        url: "/rfqs",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["RFQs"],
    }),

    updateRFQ: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/rfqs/${id}`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "RFQs", id },
        "RFQs",
      ],
    }),

    deleteRFQ: builder.mutation({
      query: (id) => ({
        url: `/rfqs/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["RFQs"],
    }),

    sendRFQ: builder.mutation({
      query: ({ id, email_data }) => ({
        url: `/rfqs/${id}/send`,
        method: "POST",
        body: email_data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "RFQs", id },
        "RFQs",
      ],
    }),

    closeRFQ: builder.mutation({
      query: (id) => ({
        url: `/rfqs/${id}/close`,
        method: "POST",
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: "RFQs", id },
        "RFQs",
      ],
    }),

    cancelRFQ: builder.mutation({
      query: ({ id, reason }) => ({
        url: `/rfqs/${id}/cancel`,
        method: "POST",
        body: { reason },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "RFQs", id },
        "RFQs",
      ],
    }),

    exportRFQPDF: builder.mutation({
      query: (id) => ({
        url: `/rfqs/${id}/export-pdf`,
        method: "POST",
        responseHandler: (response) => response.blob(),
      }),
    }),

    // RFQ Responses
    getRFQResponses: builder.query({
      query: (params) => ({
        url: "/rfq-responses",
        method: "GET",
        params: params,
      }),
      providesTags: ["RFQResponses"],
    }),

    getRFQResponse: builder.query({
      query: (id) => ({
        url: `/rfq-responses/${id}`,
        method: "GET",
      }),
      providesTags: (_result, _error, id) => [{ type: "RFQResponses", id }],
    }),

    createRFQResponse: builder.mutation({
      query: (payload) => ({
        url: "/rfq-responses",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["RFQResponses", "RFQs"],
    }),

    updateRFQResponse: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/rfq-responses/${id}`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "RFQResponses", id },
        "RFQResponses",
        "RFQs",
      ],
    }),

    deleteRFQResponse: builder.mutation({
      query: (id) => ({
        url: `/rfq-responses/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["RFQResponses", "RFQs"],
    }),

    flagRFQResponse: builder.mutation({
      query: ({ id, flag_type, notes }) => ({
        url: `/rfq-responses/${id}/flag`,
        method: "POST",
        body: { flag_type, notes },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "RFQResponses", id },
        "RFQResponses",
        "RFQs",
      ],
    }),

    uploadRFQResponseDocument: builder.mutation({
      query: ({ id, document }) => {
        const formData = new FormData();
        formData.append('document', document);
        return {
          url: `/rfq-responses/${id}/upload-document`,
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: (_result, _error, { id }) => [
        { type: "RFQResponses", id },
        "RFQResponses",
      ],
    }),

    // Bid Analysis Endpoints
    getBidAnalyses: builder.query({
      query: (params) => ({
        url: "/bid-analyses",
        method: "GET",
        params: params,
      }),
      providesTags: ["BidAnalyses"],
    }),

    getBidAnalysis: builder.query({
      query: (id) => ({
        url: `/bid-analyses/${id}`,
        method: "GET",
      }),
      providesTags: (_result, _error, id) => [{ type: "BidAnalyses", id }],
    }),

    createBidAnalysis: builder.mutation({
      query: (payload) => ({
        url: "/bid-analyses",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["BidAnalyses"],
    }),

    updateBidAnalysis: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/bid-analyses/${id}`,
        method: "PUT",
        body: payload,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "BidAnalyses", id },
        "BidAnalyses"
      ],
    }),

    deleteBidAnalysis: builder.mutation({
      query: (id) => ({
        url: `/bid-analyses/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["BidAnalyses"],
    }),

    // Supplier Selection
    selectSupplier: builder.mutation({
      query: ({ bid_analysis_id, item_id, supplier_data }) => ({
        url: `/bid-analyses/${bid_analysis_id}/items/${item_id}/select-supplier/`,
        method: "POST",
        body: supplier_data,
      }),
      invalidatesTags: (_result, _error, { bid_analysis_id }) => [
        { type: "BidAnalyses", id: bid_analysis_id },
        "BidAnalyses"
      ],
    }),

    bulkSelectSupplier: builder.mutation({
      query: ({ bid_analysis_id, selection_data }) => ({
        url: `/bid-analyses/${bid_analysis_id}/bulk-select-supplier/`,
        method: "POST",
        body: selection_data,
      }),
      invalidatesTags: (_result, _error, { bid_analysis_id }) => [
        { type: "BidAnalyses", id: bid_analysis_id },
        "BidAnalyses"
      ],
    }),

    // Split Award Management
    createSplitAward: builder.mutation({
      query: ({ bid_analysis_id, item_id, split_data }) => ({
        url: `/bid-analyses/${bid_analysis_id}/items/${item_id}/split-award/`,
        method: "POST",
        body: split_data,
      }),
      invalidatesTags: (_result, _error, { bid_analysis_id }) => [
        { type: "BidAnalyses", id: bid_analysis_id },
        "BidAnalyses"
      ],
    }),

    updateSplitAward: builder.mutation({
      query: ({ bid_analysis_id, item_id, split_id, split_data }) => ({
        url: `/bid-analyses/${bid_analysis_id}/items/${item_id}/split-award/${split_id}/`,
        method: "PUT",
        body: split_data,
      }),
      invalidatesTags: (_result, _error, { bid_analysis_id }) => [
        { type: "BidAnalyses", id: bid_analysis_id },
        "BidAnalyses"
      ],
    }),

    deleteSplitAward: builder.mutation({
      query: ({ bid_analysis_id, item_id, split_id }) => ({
        url: `/bid-analyses/${bid_analysis_id}/items/${item_id}/split-award/${split_id}/`,
        method: "DELETE",
      }),
      invalidatesTags: (_result, _error, { bid_analysis_id }) => [
        { type: "BidAnalyses", id: bid_analysis_id },
        "BidAnalyses"
      ],
    }),

    // Bid Analysis Workflow
    submitBidAnalysis: builder.mutation({
      query: (id) => ({
        url: `/bid-analyses/${id}/submit/`,
        method: "POST",
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: "BidAnalyses", id },
        "BidAnalyses"
      ],
    }),

    approveBidAnalysis: builder.mutation({
      query: ({ id, approval_data }) => ({
        url: `/bid-analyses/${id}/approve/`,
        method: "POST",
        body: approval_data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "BidAnalyses", id },
        "BidAnalyses"
      ],
    }),

    rejectBidAnalysis: builder.mutation({
      query: ({ id, rejection_data }) => ({
        url: `/bid-analyses/${id}/reject/`,
        method: "POST",
        body: rejection_data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "BidAnalyses", id },
        "BidAnalyses"
      ],
    }),

    // LPO Generation
    generateLPOFromBidAnalysis: builder.mutation({
      query: ({ id, lpo_data }) => ({
        url: `/bid-analyses/${id}/generate-lpo/`,
        method: "POST",
        body: lpo_data,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: "BidAnalyses", id },
        "BidAnalyses",
        "PurchaseOrders"
      ],
    }),

    // Bid Analysis Reports
    exportBidAnalysisPDF: builder.mutation({
      query: (id) => ({
        url: `/bid-analyses/${id}/export-pdf/`,
        method: "GET",
        responseHandler: (response) => response.blob(),
      }),
    }),

    getBidAnalysisAuditTrail: builder.query({
      query: (id) => ({
        url: `/bid-analyses/${id}/audit-trail/`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "BidAnalyses", id }],
    }),

    // Supporting Data Endpoints
    getProducts: builder.query({
      query: (params) => ({
        url: "/products",
        method: "GET",
        params: params,
      }),
    }),

    getCostCenters: builder.query({
      query: (params) => ({
        url: "/cost-centers",
        method: "GET",
        params: params,
      }),
    }),

    getStores: builder.query({
      query: (params) => ({
        url: "/stores",
        method: "GET",
        params: params,
      }),
    }),

    getUnitsOfMeasure: builder.query({
      query: (params) => ({
        url: "/units-of-measure",
        method: "GET",
        params: params,
      }),
    }),

    // Purchase Requisition Supporting Data
    getProcurementOfficers: builder.query({
      query: (params) => ({
        url: "/procurement-officers",
        method: "GET",
        params: params,
      }),
    }),

    getDepartments: builder.query({
      query: (params) => ({
        url: "/departments",
        method: "GET",
        params: params,
      }),
    }),

    getSupplierCategories: builder.query({
      query: (params) => ({
        url: "/supplier-categories",
        method: "GET",
        params: params,
      }),
    }),

    // Purchase Order Supporting Data
    getSuppliers: builder.query({
      query: (params) => ({
        url: "/suppliers",
        method: "GET",
        params: params,
      }),
    }),

    getPaymentTerms: builder.query({
      query: (params) => ({
        url: "/payment-terms",
        method: "GET",
        params: params,
      }),
    }),

    getCurrencies: builder.query({
      query: (params) => ({
        url: "/currencies",
        method: "GET",
        params: params,
      }),
    }),
  }),
});

export const {
  // Store Requisitions
  useGetStoreRequisitionsQuery,
  useGetStoreRequisitionQuery,
  useCreateStoreRequisitionMutation,
  useUpdateStoreRequisitionMutation,
  useDeleteStoreRequisitionMutation,
  useSubmitStoreRequisitionMutation,
  useApproveStoreRequisitionMutation,
  useRejectStoreRequisitionMutation,
  useConvertToPurchaseRequisitionMutation,
  
  // Purchase Requisitions
  useGetPurchaseRequisitionsQuery,
  useGetPurchaseRequisitionQuery,
  useCreatePurchaseRequisitionMutation,
  useUpdatePurchaseRequisitionMutation,
  useDeletePurchaseRequisitionMutation,
  useSubmitPurchaseRequisitionMutation,
  useApprovePurchaseRequisitionMutation,
  useRejectPurchaseRequisitionMutation,
  useAssignPurchaseRequisitionMutation,
  useConvertToRFQMutation,

  // Purchase Orders
  useGetPurchaseOrdersQuery,
  useGetPurchaseOrderQuery,
  useCreatePurchaseOrderMutation,
  useUpdatePurchaseOrderMutation,
  useDeletePurchaseOrderMutation,
  useSubmitPurchaseOrderMutation,
  useApprovePurchaseOrderMutation,
  useRejectPurchaseOrderMutation,
  useSendPurchaseOrderEmailMutation,
  useExportPurchaseOrderPDFMutation,

  // RFQs
  useGetRFQsQuery,
  useGetRFQQuery,
  useCreateRFQMutation,
  useUpdateRFQMutation,
  useDeleteRFQMutation,
  useSendRFQMutation,
  useCloseRFQMutation,
  useCancelRFQMutation,
  useExportRFQPDFMutation,

  // RFQ Responses
  useGetRFQResponsesQuery,
  useGetRFQResponseQuery,
  useCreateRFQResponseMutation,
  useUpdateRFQResponseMutation,
  useDeleteRFQResponseMutation,
  useFlagRFQResponseMutation,
  useUploadRFQResponseDocumentMutation,

  // Bid Analysis
  useGetBidAnalysesQuery,
  useGetBidAnalysisQuery,
  useCreateBidAnalysisMutation,
  useUpdateBidAnalysisMutation,
  useDeleteBidAnalysisMutation,
  useSelectSupplierMutation,
  useBulkSelectSupplierMutation,
  useCreateSplitAwardMutation,
  useUpdateSplitAwardMutation,
  useDeleteSplitAwardMutation,
  useSubmitBidAnalysisMutation,
  useApproveBidAnalysisMutation,
  useRejectBidAnalysisMutation,
  useGenerateLPOFromBidAnalysisMutation,
  useExportBidAnalysisPDFMutation,
  useGetBidAnalysisAuditTrailQuery,

  // Supporting Data
  useGetProductsQuery,
  useGetCostCentersQuery,
  useGetStoresQuery,
  useGetUnitsOfMeasureQuery,
  useGetProcurementOfficersQuery,
  useGetDepartmentsQuery,
  useGetSupplierCategoriesQuery,
  useGetSuppliersQuery,
  useGetPaymentTermsQuery,
  useGetCurrenciesQuery,
} = procurementApiSlice;
