import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Screen } from "@/app-components/layout/screen";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  useGetPurchaseRequisitionQuery,
  useSubmitPurchaseRequisitionMutation,
  useApprovePurchaseRequisitionMutation,
  useRejectPurchaseRequisitionMutation,
  useAssignPurchaseRequisitionMutation,
  useConvertToRFQMutation,
  useGetProcurementOfficersQuery,
} from "@/redux/slices/procurement";
import { 
  ArrowLeft, 
  Send, 
  CheckCircle, 
  XCircle, 
  FileText, 
  Calendar,
  User,
  Building,
  Package,
  Loader2,
  Users,
  DollarSign,
  AlertTriangle
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const PurchaseRequisitionDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [selectedOfficer, setSelectedOfficer] = useState("");

  const { data: requisition, isLoading, error } = useGetPurchaseRequisitionQuery(id!);
  const { data: procurementOfficers } = useGetProcurementOfficersQuery({});
  
  const [submitRequisition, { isLoading: submitting }] = useSubmitPurchaseRequisitionMutation();
  const [approveRequisition, { isLoading: approving }] = useApprovePurchaseRequisitionMutation();
  const [rejectRequisition, { isLoading: rejecting }] = useRejectPurchaseRequisitionMutation();
  const [assignRequisition, { isLoading: assigning }] = useAssignPurchaseRequisitionMutation();
  const [convertToRFQ, { isLoading: converting }] = useConvertToRFQMutation();

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
      Submitted: { variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      Approved: { variant: "default" as const, color: "bg-green-100 text-green-800" },
      Rejected: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
      "Converted to RFQ": { variant: "default" as const, color: "bg-purple-100 text-purple-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      Low: { color: "bg-gray-100 text-gray-800", icon: null },
      Medium: { color: "bg-yellow-100 text-yellow-800", icon: null },
      High: { color: "bg-orange-100 text-orange-800", icon: AlertTriangle },
      Urgent: { color: "bg-red-100 text-red-800", icon: AlertTriangle },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.Low;
    
    return (
      <Badge className={config.color}>
        {config.icon && <config.icon className="w-3 h-3 mr-1" />}
        {priority}
      </Badge>
    );
  };

  const handleSubmit = async () => {
    try {
      await submitRequisition(id!).unwrap();
      toast.success("Purchase requisition submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit requisition");
    }
  };

  const handleApprove = async () => {
    try {
      await approveRequisition(id!).unwrap();
      toast.success("Purchase requisition approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve requisition");
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }

    try {
      await rejectRequisition({ id: id!, reason: rejectReason }).unwrap();
      toast.success("Purchase requisition rejected");
      setShowRejectDialog(false);
      setRejectReason("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to reject requisition");
    }
  };

  const handleAssign = async () => {
    if (!selectedOfficer) {
      toast.error("Please select a procurement officer");
      return;
    }

    try {
      await assignRequisition({ id: id!, assigned_to: Number(selectedOfficer) }).unwrap();
      toast.success("Purchase requisition assigned successfully");
      setShowAssignDialog(false);
      setSelectedOfficer("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to assign requisition");
    }
  };

  const handleConvertToRFQ = async () => {
    try {
      await convertToRFQ(id!).unwrap();
      toast.success("Successfully converted to RFQ");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to convert to RFQ");
    }
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Screen>
    );
  }

  if (error || !requisition?.data) {
    return (
      <Screen>
        <div className="text-center py-8">
          <p className="text-red-600">Failed to load purchase requisition details</p>
          <Button onClick={() => navigate(-1)} className="mt-4">
            Go Back
          </Button>
        </div>
      </Screen>
    );
  }

  const req = requisition.data;

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                Purchase Requisition {req.pr_number || `PR-${String(req.id).padStart(4, '0')}`}
              </h1>
              <p className="text-gray-600 mt-1">
                {req.store_requisition_number && `From Store Requisition: ${req.store_requisition_number}`}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {getStatusBadge(req.status!)}
            {req.priority && getPriorityBadge(req.priority)}
            
            {/* Action Buttons */}
            {req.status === "Draft" && (
              <Button onClick={handleSubmit} disabled={submitting}>
                {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Send className="mr-2 h-4 w-4" />
                Submit
              </Button>
            )}
            
            {req.status === "Submitted" && (
              <>
                <Button onClick={() => setShowAssignDialog(true)}>
                  <Users className="mr-2 h-4 w-4" />
                  Assign Officer
                </Button>
                <Button onClick={handleApprove} disabled={approving}>
                  {approving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve
                </Button>
                <Button 
                  variant="destructive" 
                  onClick={() => setShowRejectDialog(true)}
                  disabled={rejecting}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </Button>
              </>
            )}
            
            {req.status === "Approved" && (
              <Button onClick={handleConvertToRFQ} disabled={converting}>
                {converting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <FileText className="mr-2 h-4 w-4" />
                Convert to RFQ
              </Button>
            )}
          </div>
        </div>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Purchase Requisition Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Created By</p>
                  <p className="font-medium">{req.created_by_name || "N/A"}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Assigned To</p>
                  <p className="font-medium">{req.assigned_to_name || "Unassigned"}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Building className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Department</p>
                  <p className="font-medium">{req.department || "N/A"}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Package className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Supplier Category</p>
                  <p className="font-medium">{req.supplier_category || "N/A"}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Required By</p>
                  <p className="font-medium">
                    {req.required_date ? new Date(req.required_date).toLocaleDateString() : "N/A"}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Estimated Cost</p>
                  <p className="font-medium">
                    {req.total_estimated_cost
                      ? `${req.currency || "KES"} ${req.total_estimated_cost.toLocaleString()}`
                      : "N/A"
                    }
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Building className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Cost Center</p>
                  <p className="font-medium">{req.cost_center_name || "N/A"}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Budget Code</p>
                  <p className="font-medium">{req.budget_code || "N/A"}</p>
                </div>
              </div>
            </div>

            {req.approval_notes && (
              <>
                <Separator className="my-4" />
                <div>
                  <p className="text-sm text-gray-500 mb-2">Approval Notes</p>
                  <p className="text-gray-800">{req.approval_notes}</p>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Items */}
        <Card>
          <CardHeader>
            <CardTitle>Requested Items</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Est. Unit Cost</TableHead>
                  <TableHead>Est. Total</TableHead>
                  <TableHead>Specifications</TableHead>
                  <TableHead>Preferred Supplier</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {req.items?.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      <div>
                        <p>{item.product_name || `Product ID: ${item.product}`}</p>
                        {item.product_code && (
                          <p className="text-sm text-gray-500">Code: {item.product_code}</p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>{item.unit_of_measure_name || "N/A"}</TableCell>
                    <TableCell>
                      {item.estimated_unit_cost
                        ? `${req.currency || "KES"} ${item.estimated_unit_cost.toLocaleString()}`
                        : "N/A"
                      }
                    </TableCell>
                    <TableCell>
                      {item.estimated_total_cost
                        ? `${req.currency || "KES"} ${item.estimated_total_cost.toLocaleString()}`
                        : "N/A"
                      }
                    </TableCell>
                    <TableCell>{item.specifications || "-"}</TableCell>
                    <TableCell>{item.preferred_supplier || "-"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Timestamps */}
        <Card>
          <CardHeader>
            <CardTitle>Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Created At</p>
                <p className="font-medium">
                  {req.created_at ? new Date(req.created_at).toLocaleString() : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Last Updated</p>
                <p className="font-medium">
                  {req.updated_at ? new Date(req.updated_at).toLocaleString() : "N/A"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Purchase Requisition</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reject-reason">Reason for Rejection *</Label>
              <Textarea
                id="reject-reason"
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                placeholder="Please provide a reason for rejecting this requisition..."
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleReject} disabled={rejecting}>
              {rejecting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Reject Requisition
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assign Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Procurement Officer</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="procurement-officer">Select Procurement Officer *</Label>
              <Select value={selectedOfficer} onValueChange={setSelectedOfficer}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a procurement officer" />
                </SelectTrigger>
                <SelectContent>
                  {procurementOfficers?.data?.results?.map((officer: any) => (
                    <SelectItem key={officer.id} value={officer.id.toString()}>
                      {officer.name} - {officer.department}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAssignDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAssign} disabled={assigning}>
              {assigning && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Assign Officer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Screen>
  );
};

export default PurchaseRequisitionDetail;
