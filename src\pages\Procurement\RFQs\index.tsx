import { Screen } from "@/app-components/layout/screen";
import AddRFQ from "./modals/AddRFQ";
import { useState } from "react";
import {
  useGetRFQsQuery,
  useGetSuppliersQuery,
  useGetStoresQuery,
  useSendRFQMutation,
  useCloseRFQMutation,
  useCancelRFQMutation,
  useDeleteRFQMutation,
  useExportRFQPDFMutation
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { RFQ } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { 
  Plus, 
  MoreHorizontal,
  Eye,
  Send,
  CheckCircle,
  XCircle,
  Trash2,
  Download,
  Search,
  FileText,
  Clock,
  Users
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const RFQsIndex = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [filters, setFilters] = useState({
    status: "all",
    supplier: "all",
    delivery_location: "all",
    category: "all",
    search: "",
  });

  // Transform filters for API (remove "all" values)
  const apiFilters = Object.fromEntries(
    Object.entries(filters).filter(([key, value]) => value !== "all" && value !== "")
  );

  // API queries
  const { data: rfqsData, isLoading } = useGetRFQsQuery(apiFilters);
  const { data: suppliers } = useGetSuppliersQuery({});
  const { data: stores } = useGetStoresQuery({});

  // Mutation hooks
  const [sendRFQ] = useSendRFQMutation();
  const [closeRFQ] = useCloseRFQMutation();
  const [cancelRFQ] = useCancelRFQMutation();
  const [deleteRFQ] = useDeleteRFQMutation();
  const [exportRFQPDF] = useExportRFQPDFMutation();

  // Handler functions
  const handleSend = async (id: number) => {
    try {
      await sendRFQ({ id, email_data: {} }).unwrap();
      toast.success("RFQ sent to suppliers successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to send RFQ");
    }
  };

  const handleClose = async (id: number) => {
    try {
      await closeRFQ(id).unwrap();
      toast.success("RFQ closed successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to close RFQ");
    }
  };

  const handleCancel = async (id: number, reason: string) => {
    try {
      await cancelRFQ({ id, reason }).unwrap();
      toast.success("RFQ cancelled");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to cancel RFQ");
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this RFQ?")) {
      try {
        await deleteRFQ(id).unwrap();
        toast.success("RFQ deleted successfully");
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to delete RFQ");
      }
    }
  };

  const handleExportPDF = async (id: number) => {
    try {
      const blob = await exportRFQPDF(id).unwrap();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `rfq-${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("RFQ exported successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to export RFQ");
    }
  };

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Open": { variant: "secondary" as const, className: "bg-green-100 text-green-800" },
      "Closed": { variant: "secondary" as const, className: "bg-blue-100 text-blue-800" },
      "Cancelled": { variant: "secondary" as const, className: "bg-red-100 text-red-800" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Open"];
    return <Badge variant={config.variant} className={config.className}>{status}</Badge>;
  };

  // Search handler
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  // Table columns
  const columns: ColumnDef<RFQ>[] = [
    {
      accessorKey: "rfq_number",
      header: "RFQ Number",
      cell: ({ row }) => (
        <Link 
          to={`/procurement/rfqs/${row.original.id}`}
          className="font-medium text-blue-600 hover:text-blue-800"
        >
          {row.original.rfq_number}
        </Link>
      ),
    },
    {
      accessorKey: "requisition_number",
      header: "From Requisition",
      cell: ({ row }) => (
        <div className="font-medium">{row.original.requisition_number || "-"}</div>
      ),
    },
    {
      accessorKey: "supplier_names",
      header: "Suppliers",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Users className="h-4 w-4 text-gray-500" />
          <span className="text-sm">
            {row.original.supplier_names?.length || 0} supplier(s)
          </span>
        </div>
      ),
    },
    {
      accessorKey: "response_deadline",
      header: "Response Deadline",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Clock className="h-4 w-4 text-gray-500" />
          <span className="text-sm">
            {row.original.response_deadline 
              ? new Date(row.original.response_deadline).toLocaleDateString()
              : "-"
            }
          </span>
        </div>
      ),
    },
    {
      accessorKey: "response_count",
      header: "Responses",
      cell: ({ row }) => (
        <div className="text-center">
          <Badge variant="outline">
            {row.original.response_count || 0} received
          </Badge>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => getStatusBadge(row.original.status || "Open"),
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => (
        <div className="text-sm">
          <div>{new Date(row.original.created_at || "").toLocaleDateString()}</div>
          <div className="text-gray-500">{row.original.created_by_name}</div>
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const rfq = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to={`/procurement/rfqs/${rfq.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              
              {rfq.status === "Open" && (
                <>
                  <DropdownMenuItem onClick={() => handleSend(rfq.id!)}>
                    <Send className="mr-2 h-4 w-4" />
                    Send to Suppliers
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleClose(rfq.id!)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Close RFQ
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleCancel(rfq.id!, "Cancelled by user")}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Cancel
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleDelete(rfq.id!)}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
              
              <DropdownMenuItem onClick={() => handleExportPDF(rfq.id!)}>
                <Download className="mr-2 h-4 w-4" />
                Export PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Request for Quotations (RFQ)</h1>
            <p className="text-gray-600 mt-1">Manage RFQs and track supplier responses</p>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={() => setShowAddModal(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create RFQ
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search RFQs..."
              className="w-64"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          
          <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Open">Open</SelectItem>
              <SelectItem value="Closed">Closed</SelectItem>
              <SelectItem value="Cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.supplier} onValueChange={(value) => setFilters(prev => ({ ...prev, supplier: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by supplier" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Suppliers</SelectItem>
              {suppliers?.data?.results?.map((supplier: any) => (
                <SelectItem key={supplier.id} value={supplier.id.toString()}>
                  {supplier.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.delivery_location} onValueChange={(value) => setFilters(prev => ({ ...prev, delivery_location: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Locations</SelectItem>
              {stores?.data?.results?.map((store: any) => (
                <SelectItem key={store.id} value={store.id.toString()}>
                  {store.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Data Table */}
        <DataTable
          columns={columns}
          data={rfqsData?.data?.results || []}
          loading={isLoading}
        />

        {/* Add RFQ Modal */}
        <AddRFQ
          open={showAddModal}
          onClose={() => setShowAddModal(false)}
        />
      </div>
    </Screen>
  );
};

export default RFQsIndex;
