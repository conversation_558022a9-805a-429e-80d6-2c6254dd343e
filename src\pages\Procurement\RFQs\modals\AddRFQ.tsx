import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Loader2, Package, Building, MapPin, Clock, X } from "lucide-react";
import {
  useCreateRFQMutation,
  useGetPurchaseRequisitionsQuery,
  useGetSuppliersQuery,
  useGetStoresQuery,
  useGetProductsQuery,
  useGetUnitsOfMeasureQuery,
} from "@/redux/slices/procurement";
import { RFQFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddRFQProps {
  open: boolean;
  onClose: () => void;
}

const AddRFQ = ({ open, onClose }: AddRFQProps) => {
  const [createRFQ, { isLoading: creating }] = useCreateRFQMutation();
  
  // Fetch supporting data
  const { data: purchaseRequisitions } = useGetPurchaseRequisitionsQuery({ status: "Approved" });
  const { data: suppliers } = useGetSuppliersQuery({});
  const { data: stores } = useGetStoresQuery({});
  const { data: products } = useGetProductsQuery({});
  const { data: unitsOfMeasure } = useGetUnitsOfMeasureQuery({});

  const [formData, setFormData] = useState<RFQFormData>({
    requisition: "direct",
    suppliers: [],
    delivery_location: "",
    delivery_address: "",
    required_date: "",
    response_deadline: "",
    notes: "",
    terms_and_conditions: "",
    items: [
      {
        product: "",
        quantity: "",
        unit_of_measure: "",
        specifications: "",
        estimated_unit_cost: "",
      },
    ],
  });

  const [selectedSuppliers, setSelectedSuppliers] = useState<{[key: number]: boolean}>({});

  const handleInputChange = (field: keyof RFQFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSupplierToggle = (supplierId: number, checked: boolean) => {
    setSelectedSuppliers(prev => ({
      ...prev,
      [supplierId]: checked
    }));

    const updatedSuppliers = checked 
      ? [...formData.suppliers, supplierId]
      : formData.suppliers.filter(id => id !== supplierId);
    
    handleInputChange("suppliers", updatedSuppliers);
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const addItem = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          product: "",
          quantity: "",
          unit_of_measure: "",
          specifications: "",
          estimated_unit_cost: "",
        },
      ],
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      setFormData((prev) => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index),
      }));
    }
  };

  const loadFromRequisition = (requisitionId: number) => {
    const requisition = purchaseRequisitions?.data?.results?.find(
      (req: any) => req.id === requisitionId
    );
    
    if (requisition) {
      setFormData(prev => ({
        ...prev,
        delivery_location: requisition.store || "",
        required_date: requisition.required_date || "",
        items: requisition.items?.map((item: any) => ({
          product: item.product || "",
          quantity: item.quantity || "",
          unit_of_measure: item.unit_of_measure || "",
          specifications: item.specifications || "",
          estimated_unit_cost: item.estimated_unit_cost || "",
        })) || prev.items,
      }));
    }
  };

  const resetForm = () => {
    setFormData({
      requisition: "direct",
      suppliers: [],
      delivery_location: "",
      delivery_address: "",
      required_date: "",
      response_deadline: "",
      notes: "",
      terms_and_conditions: "",
      items: [
        {
          product: "",
          quantity: "",
          unit_of_measure: "",
          specifications: "",
          estimated_unit_cost: "",
        },
      ],
    });
    setSelectedSuppliers({});
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (formData.suppliers.length === 0) {
      toast.error("Please select at least one supplier");
      return;
    }

    if (!formData.delivery_location) {
      toast.error("Please select a delivery location");
      return;
    }

    if (!formData.response_deadline) {
      toast.error("Please set a response deadline");
      return;
    }

    const validItems = formData.items.filter(
      (item) => item.product && item.quantity
    );

    if (validItems.length === 0) {
      toast.error("Please add at least one valid item");
      return;
    }

    try {
      const payload = {
        ...formData,
        requisition: formData.requisition === "direct" ? null : formData.requisition,
        items: validItems,
      };

      await createRFQ(payload).unwrap();
      toast.success("RFQ created successfully");
      resetForm();
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create RFQ");
    }
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  useEffect(() => {
    if (formData.requisition && formData.requisition !== "direct") {
      loadFromRequisition(Number(formData.requisition));
    }
  }, [formData.requisition, purchaseRequisitions]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Create Request for Quotation (RFQ)
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Package className="h-4 w-4" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="requisition">From Purchase Requisition (Optional)</Label>
                  <Select
                    value={formData.requisition ? formData.requisition.toString() : "direct"}
                    onValueChange={(value) => handleInputChange("requisition", value === "direct" ? "" : parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select purchase requisition" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="direct">Create Direct RFQ</SelectItem>
                      {purchaseRequisitions?.data?.results?.map((req: any) => (
                        <SelectItem key={req.id} value={req.id.toString()}>
                          {req.requisition_number} - {req.purpose}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="response_deadline">Response Deadline *</Label>
                  <Input
                    id="response_deadline"
                    type="datetime-local"
                    value={formData.response_deadline}
                    onChange={(e) => handleInputChange("response_deadline", e.target.value)}
                    min={new Date().toISOString().slice(0, 16)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Supplier Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Building className="h-4 w-4" />
                Supplier Selection *
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-60 overflow-y-auto">
                {suppliers?.data?.results?.map((supplier: any) => (
                  <div key={supplier.id} className="flex items-center space-x-2 p-2 border rounded">
                    <Checkbox
                      id={`supplier-${supplier.id}`}
                      checked={selectedSuppliers[supplier.id] || false}
                      onCheckedChange={(checked) => handleSupplierToggle(supplier.id, checked as boolean)}
                    />
                    <Label htmlFor={`supplier-${supplier.id}`} className="text-sm">
                      {supplier.name}
                    </Label>
                  </div>
                ))}
              </div>
              
              {formData.suppliers.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-4">
                  <Label className="text-sm font-medium">Selected Suppliers:</Label>
                  {formData.suppliers.map(supplierId => {
                    const supplier = suppliers?.data?.results?.find((s: any) => s.id === supplierId);
                    return (
                      <Badge key={supplierId} variant="secondary" className="flex items-center gap-1">
                        {supplier?.name}
                        <X 
                          className="h-3 w-3 cursor-pointer" 
                          onClick={() => handleSupplierToggle(supplierId, false)}
                        />
                      </Badge>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Delivery Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <MapPin className="h-4 w-4" />
                Delivery Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="delivery_location">Delivery Location *</Label>
                  <Select
                    value={formData.delivery_location ? formData.delivery_location.toString() : ""}
                    onValueChange={(value) => handleInputChange("delivery_location", parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select delivery location" />
                    </SelectTrigger>
                    <SelectContent>
                      {stores?.data?.results?.map((store: any) => (
                        <SelectItem key={store.id} value={store.id.toString()}>
                          {store.name} - {store.location}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="required_date">Required Date</Label>
                  <Input
                    id="required_date"
                    type="date"
                    value={formData.required_date}
                    onChange={(e) => handleInputChange("required_date", e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="delivery_address">Specific Delivery Address</Label>
                <Textarea
                  id="delivery_address"
                  placeholder="Enter specific delivery address if different from location..."
                  value={formData.delivery_address}
                  onChange={(e) => handleInputChange("delivery_address", e.target.value)}
                  rows={2}
                />
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Package className="h-4 w-4" />
                RFQ Items
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    {formData.items.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <Label>Product *</Label>
                      <Select
                        value={item.product ? item.product.toString() : ""}
                        onValueChange={(value) => handleItemChange(index, "product", parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {products?.data?.results?.map((product: any) => (
                            <SelectItem key={product.id} value={product.id.toString()}>
                              {product.name} ({product.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Quantity *</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, "quantity", e.target.value)}
                      />
                    </div>

                    <div>
                      <Label>Unit of Measure</Label>
                      <Select
                        value={item.unit_of_measure ? item.unit_of_measure.toString() : ""}
                        onValueChange={(value) => handleItemChange(index, "unit_of_measure", parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {unitsOfMeasure?.data?.results?.map((unit: any) => (
                            <SelectItem key={unit.id} value={unit.id.toString()}>
                              {unit.name} ({unit.abbreviation})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Specifications</Label>
                      <Textarea
                        placeholder="Enter product specifications..."
                        value={item.specifications}
                        onChange={(e) => handleItemChange(index, "specifications", e.target.value)}
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label>Estimated Unit Cost</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        value={item.estimated_unit_cost}
                        onChange={(e) => handleItemChange(index, "estimated_unit_cost", e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addItem}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </CardContent>
          </Card>

          {/* Notes and Terms */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Additional Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes or instructions for suppliers..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="terms_and_conditions">Terms and Conditions</Label>
                <Textarea
                  id="terms_and_conditions"
                  placeholder="Enter terms and conditions for this RFQ..."
                  value={formData.terms_and_conditions}
                  onChange={(e) => handleInputChange("terms_and_conditions", e.target.value)}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create RFQ"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddRFQ;
