export interface BankDetails {
  bank_name?: string;
  account_name?: string;
  account_number?: string;
  branch_name?: string;
  swift_code?: string;
  swift_iban?: string;
  bank_code?: string;
}

export interface supplierType {
  id?: string;
  name?: string;
  category?: string;
  branch?: string;
  code?: string;
  status?: string;
  contact_name?: string;
  email?: string;
  phone_number?: string;
  alt_phone_number?: string;
  address_physical?: string;
  address_mailing?: string;
  country?: string;
  city_or_state?: string;
  tax_vat_16?: boolean;
  tax_exempt?: boolean;
  payment_terms?: string;
  is_active?: boolean;
  is_blacklisted?: boolean;
  gl_account?: string;
  bank_details?: BankDetails;
}
