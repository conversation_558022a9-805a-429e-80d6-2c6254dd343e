import { apiSlice } from "../apiSlice";

// Types for Tax Rate API
export interface TaxRate {
  id?: number;
  name?: string;
  rate?: number;
  description?: string;
  is_active?: boolean;
}

export const taxRateApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all tax rates
    getTaxRates: builder.query<TaxRate[], any>({
      query: (params) => ({
        url: "/setup/tax-rates",
        method: "GET",
        params: params,
      }),
      providesTags: ["TaxRates"],
    }),

    // Get single tax rate
    getTaxRate: builder.query<TaxRate, string>({
      query: (id) => ({
        url: `/setup/tax-rates/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "TaxRates", id }],
    }),

    // Create tax rate
    createTaxRate: builder.mutation<TaxRate, Partial<TaxRate>>({
      query: (payload) => ({
        url: "/setup/tax-rates",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["TaxRates"],
    }),

    // Update tax rate
    updateTaxRate: builder.mutation<TaxRate, { id: string; data: Partial<TaxRate> }>({
      query: ({ id, data }) => ({
        url: `/setup/tax-rates/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "TaxRates", id }, "TaxRates"],
    }),

    // Delete tax rate
    deleteTaxRate: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/tax-rates/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["TaxRates"],
    }),
  }),
});

export const { 
  useGetTaxRatesQuery, 
  useGetTaxRateQuery,
  useCreateTaxRateMutation,
  useUpdateTaxRateMutation,
  useDeleteTaxRateMutation,
} = taxRateApiSlice;
