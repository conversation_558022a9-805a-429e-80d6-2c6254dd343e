import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  ShoppingCart,
  Package,
  Clock,
  CheckCircle,
  ArrowRight,
  TrendingUp
} from "lucide-react";
import { Link } from "react-router-dom";

const ProcurementIndex = () => {
  // Mock data - replace with actual API calls
  const stats = {
    storeRequisitions: {
      total: 45,
      pending: 12,
      approved: 28,
      rejected: 5
    },
    purchaseRequisitions: {
      total: 32,
      pending: 8,
      approved: 20,
      rejected: 4
    },
    purchaseOrders: {
      total: 18,
      pending: 5,
      approved: 10,
      completed: 3
    }
  };

  const quickActions = [
    {
      title: "Create Store Requisition",
      description: "Request items from store inventory",
      icon: FileText,
      href: "/procurement/store-requisitions",
      color: "bg-blue-500"
    },
    {
      title: "Create Purchase Requisition", 
      description: "Request items for purchase",
      icon: ShoppingCart,
      href: "/procurement/purchase-requisitions",
      color: "bg-green-500"
    },
    {
      title: "Create Purchase Order",
      description: "Generate purchase order for suppliers",
      icon: Package,
      href: "/procurement/purchase-orders",
      color: "bg-purple-500"
    },
    {
      title: "Create RFQ",
      description: "Request quotations from suppliers",
      icon: FileText,
      href: "/procurement/rfqs",
      color: "bg-orange-500"
    },
    {
      title: "Bid Analysis",
      description: "Compare suppliers and select winners",
      icon: TrendingUp,
      href: "/procurement/bid-analysis",
      color: "bg-purple-500"
    }
  ];

  const recentActivity = [
    {
      id: 1,
      type: "Store Requisition",
      title: "SR-0045 - Office Supplies",
      status: "Approved",
      time: "2 hours ago",
      statusColor: "bg-green-100 text-green-800"
    },
    {
      id: 2,
      type: "Purchase Order",
      title: "PO-0023 - IT Equipment",
      status: "Pending",
      time: "4 hours ago",
      statusColor: "bg-yellow-100 text-yellow-800"
    },
    {
      id: 3,
      type: "Purchase Requisition",
      title: "PR-0067 - Maintenance Supplies",
      status: "Submitted",
      time: "1 day ago",
      statusColor: "bg-blue-100 text-blue-800"
    }
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Procurement Dashboard</h1>
            <p className="text-gray-600 mt-1">Manage your procurement processes efficiently</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Store Requisitions</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.storeRequisitions.total}</div>
              <div className="flex gap-4 mt-2 text-xs">
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-yellow-500" />
                  {stats.storeRequisitions.pending} Pending
                </span>
                <span className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  {stats.storeRequisitions.approved} Approved
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Purchase Requisitions</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.purchaseRequisitions.total}</div>
              <div className="flex gap-4 mt-2 text-xs">
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-yellow-500" />
                  {stats.purchaseRequisitions.pending} Pending
                </span>
                <span className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  {stats.purchaseRequisitions.approved} Approved
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Purchase Orders</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.purchaseOrders.total}</div>
              <div className="flex gap-4 mt-2 text-xs">
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-yellow-500" />
                  {stats.purchaseOrders.pending} Pending
                </span>
                <span className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  {stats.purchaseOrders.completed} Completed
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common procurement tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {quickActions.map((action, index) => (
                <Link key={index} to={action.href}>
                  <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${action.color} text-white`}>
                        <action.icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{action.title}</h3>
                        <p className="text-sm text-gray-600">{action.description}</p>
                      </div>
                      <ArrowRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest procurement activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      {activity.type === "Store Requisition" && <FileText className="h-4 w-4" />}
                      {activity.type === "Purchase Requisition" && <ShoppingCart className="h-4 w-4" />}
                      {activity.type === "Purchase Order" && <Package className="h-4 w-4" />}
                    </div>
                    <div>
                      <p className="font-medium">{activity.title}</p>
                      <p className="text-sm text-gray-600">{activity.type} • {activity.time}</p>
                    </div>
                  </div>
                  <Badge className={activity.statusColor}>
                    {activity.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Module Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Store Requisitions
              </CardTitle>
              <CardDescription>Manage store inventory requests</CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/procurement/store-requisitions">
                <Button className="w-full">
                  View All Store Requisitions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                Purchase Requisitions
              </CardTitle>
              <CardDescription>Manage purchase requests</CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/procurement/purchase-requisitions">
                <Button className="w-full">
                  View All Purchase Requisitions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Purchase Orders
              </CardTitle>
              <CardDescription>Manage purchase orders</CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/procurement/purchase-orders">
                <Button className="w-full">
                  View All Purchase Orders
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Request for Quotations
              </CardTitle>
              <CardDescription>Manage RFQs and supplier responses</CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/procurement/rfqs">
                <Button className="w-full">
                  View All RFQs
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Bid Analysis
              </CardTitle>
              <CardDescription>Compare suppliers and select winners</CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/procurement/bid-analysis">
                <Button className="w-full">
                  View All Analyses
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </Screen>
  );
};

export default ProcurementIndex;
