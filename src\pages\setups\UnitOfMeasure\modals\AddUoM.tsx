import { ActionButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  useAddUnitOfMeasureMutation,
  usePatchUnitOfMeasureMutation,
} from "@/redux/slices/uom";
import { UoMTypes } from "@/types/UoM";
import { Loader2, Send } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  updateData?: UoMTypes;
}

const AddUoM = ({ isOpen, onClose, updateData }: propTypes) => {
  const [createUoM, { isLoading: loading }] = useAddUnitOfMeasureMutation();
  const [updateUoM, { isLoading: loadingUpdate }] =
    usePatchUnitOfMeasureMutation();

  const [formData, setFormData] = useState({
    name: updateData ? updateData.name : "",
    symbol: updateData ? updateData.symbol : "",
    conversion_base: updateData ? updateData.conversion_base : "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddUoM = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (updateData) {
        await updateUoM({ id: updateData.id, ...formData }).unwrap();
        toast.success("Unit of Measure updated successfully");
      } else {
        await createUoM(formData).unwrap();
        toast.success("Unit of Measure added successfully");
      }
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "An error occurred");
    }
  };

  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title={updateData ? "Update Unit of Measure" : "Add Unit of Measure"}
      description="Enter unit of measure details"
    >
      <form onSubmit={handleAddUoM}>
        <div className="space-y-4 py-2">
          <div className="space-y-2">
            <Label htmlFor="name">Name*</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter unit of measure name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="symbol">symbol</Label>
            <Input
              id="symbol"
              name="symbol"
              value={formData.symbol}
              onChange={handleInputChange}
              placeholder="Auto-generated or enter manually"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="conversion_base">conversion_base</Label>
            <Textarea
              id="conversion_base"
              name="conversion_base"
              value={formData.conversion_base}
              onChange={handleInputChange}
              placeholder="Enter conversion_base"
            />
          </div>
        </div>

        <div className="flex justify-end gap-4 mt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {loading || loadingUpdate ? (
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding...
            </Button>
          ) : (
            <Button type="submit">
              {updateData ? "Update Store" : "Add Store"}
              <Send className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </form>
    </BaseModal>
  );
};

export default AddUoM;
