import { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Screen } from "@/app-components/layout/screen";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  useGetBidAnalysisQuery,
  useSubmitBidAnalysisMutation,
  useApproveBidAnalysisMutation,
  useRejectBidAnalysisMutation,
  useGenerateLPOFromBidAnalysisMutation,
  useExportBidAnalysisPDFMutation,
} from "@/redux/slices/procurement";
import { 
  ArrowLeft, 
  Send, 
  CheckCircle, 
  XCircle, 
  FileText, 
  Calendar,
  User,
  Building,
  Package,
  Loader2,
  Download,
  MapPin,
  Clock,
  DollarSign,
  TrendingUp,
  Crown,
  AlertTriangle
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import SupplierComparisonTable from "./components/SupplierComparisonTable";
import BidAnalysisAuditTrail from "./components/BidAnalysisAuditTrail";
import LPOGenerationModal from "./modals/LPOGenerationModal";

const BidAnalysisDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showLPOModal, setShowLPOModal] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [activeTab, setActiveTab] = useState<"comparison" | "audit">("comparison");

  const { data: bidAnalysis, isLoading, error } = useGetBidAnalysisQuery(id!);
  
  const [submitBidAnalysis, { isLoading: submitting }] = useSubmitBidAnalysisMutation();
  const [approveBidAnalysis, { isLoading: approving }] = useApproveBidAnalysisMutation();
  const [rejectBidAnalysis, { isLoading: rejecting }] = useRejectBidAnalysisMutation();
  const [generateLPO, { isLoading: generating }] = useGenerateLPOFromBidAnalysisMutation();
  const [exportPDF, { isLoading: exporting }] = useExportBidAnalysisPDFMutation();

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
      "Under Review": { variant: "default" as const, color: "bg-yellow-100 text-yellow-800" },
      Approved: { variant: "default" as const, color: "bg-green-100 text-green-800" },
      Rejected: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
      Converted: { variant: "default" as const, color: "bg-blue-100 text-blue-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };

  const handleSubmit = async () => {
    try {
      await submitBidAnalysis(Number(id)).unwrap();
      toast.success("Bid analysis submitted for review");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit bid analysis");
    }
  };

  const handleApprove = async () => {
    try {
      await approveBidAnalysis({ id: Number(id), approval_data: {} }).unwrap();
      toast.success("Bid analysis approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve bid analysis");
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }
    
    try {
      await rejectBidAnalysis({ id: Number(id), rejection_data: { reason: rejectReason } }).unwrap();
      toast.success("Bid analysis rejected");
      setShowRejectDialog(false);
      setRejectReason("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to reject bid analysis");
    }
  };

  const handleGenerateLPO = async () => {
    try {
      await generateLPO({ 
        id: Number(id), 
        lpo_data: { 
          group_by_supplier: true,
          generate_separate_pos: true 
        } 
      }).unwrap();
      toast.success("Purchase Orders generated successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to generate Purchase Orders");
    }
  };

  const handleExportPDF = async () => {
    try {
      const blob = await exportPDF(Number(id)).unwrap();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `bid-analysis-${bidAnalysis?.analysis_number}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Bid analysis exported successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to export bid analysis");
    }
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Screen>
    );
  }

  if (error || !bidAnalysis) {
    return (
      <Screen>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Bid Analysis Not Found</h2>
          <p className="text-gray-600 mb-4">The bid analysis you're looking for doesn't exist or has been removed.</p>
          <Button onClick={() => navigate("/procurement/bid-analysis")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Bid Analysis
          </Button>
        </div>
      </Screen>
    );
  }

  const selectedItemsCount = bidAnalysis.items?.filter(item => item.selected_supplier).length || 0;
  const totalItemsCount = bidAnalysis.items?.length || 0;
  const selectionProgress = totalItemsCount > 0 ? (selectedItemsCount / totalItemsCount) * 100 : 0;

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/procurement/bid-analysis")}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                Bid Analysis {bidAnalysis.analysis_number}
              </h1>
              <div className="flex items-center gap-2 mt-1">
                {getStatusBadge(bidAnalysis.status || "Draft")}
                <span className="text-gray-500">•</span>
                <span className="text-gray-600">
                  Created {new Date(bidAnalysis.created_at || "").toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleExportPDF}
              disabled={exporting}
            >
              {exporting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Download className="mr-2 h-4 w-4" />
              )}
              Export PDF
            </Button>

            {bidAnalysis.status === "Draft" && (
              <Button onClick={handleSubmit} disabled={submitting}>
                {submitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                Submit for Review
              </Button>
            )}

            {bidAnalysis.status === "Under Review" && (
              <>
                <Button onClick={handleApprove} disabled={approving}>
                  {approving ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle className="mr-2 h-4 w-4" />
                  )}
                  Approve
                </Button>
                
                <Button
                  variant="destructive"
                  onClick={() => setShowRejectDialog(true)}
                  disabled={rejecting}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </Button>
              </>
            )}

            {bidAnalysis.status === "Approved" && (
              <Button onClick={() => setShowLPOModal(true)} disabled={generating}>
                {generating ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Package className="mr-2 h-4 w-4" />
                )}
                Generate LPO
              </Button>
            )}
          </div>
        </div>

        {/* Progress Summary */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{totalItemsCount}</div>
                <div className="text-sm text-gray-600">Total Items</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{selectedItemsCount}</div>
                <div className="text-sm text-gray-600">Winners Selected</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {bidAnalysis.supplier_responses?.length || 0}
                </div>
                <div className="text-sm text-gray-600">Supplier Responses</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {Math.round(selectionProgress)}%
                </div>
                <div className="text-sm text-gray-600">Selection Progress</div>
              </div>
            </div>
            
            {selectionProgress < 100 && bidAnalysis.status === "Draft" && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <span className="text-yellow-800 text-sm">
                  {totalItemsCount - selectedItemsCount} item(s) still need winner selection
                </span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* RFQ Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  RFQ Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">RFQ Number</Label>
                    <p className="font-medium">{bidAnalysis.rfq_number}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Analysis Number</Label>
                    <p className="font-medium">{bidAnalysis.analysis_number}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Value Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Estimated Value</Label>
                  <p className="font-medium">
                    {bidAnalysis.currency} {bidAnalysis.total_estimated_value?.toLocaleString() || "0"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Selected Value</Label>
                  <p className="font-medium text-green-600">
                    {bidAnalysis.currency} {bidAnalysis.total_selected_value?.toLocaleString() || "0"}
                  </p>
                </div>
                {bidAnalysis.total_estimated_value && bidAnalysis.total_selected_value && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Savings</Label>
                    <p className="font-medium text-blue-600">
                      {bidAnalysis.currency} {(bidAnalysis.total_estimated_value - bidAnalysis.total_selected_value).toLocaleString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("comparison")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "comparison"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <TrendingUp className="inline-block w-4 h-4 mr-2" />
              Supplier Comparison
            </button>
            <button
              onClick={() => setActiveTab("audit")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "audit"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Clock className="inline-block w-4 h-4 mr-2" />
              Audit Trail
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === "comparison" ? (
          <SupplierComparisonTable bidAnalysis={bidAnalysis} />
        ) : (
          <BidAnalysisAuditTrail bidAnalysisId={Number(id)} />
        )}

        {/* Reject Dialog */}
        <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reject Bid Analysis</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="reject-reason">Reason for Rejection</Label>
                <Textarea
                  id="reject-reason"
                  placeholder="Please provide a reason for rejecting this bid analysis..."
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowRejectDialog(false);
                  setRejectReason("");
                }}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleReject}
                disabled={rejecting || !rejectReason.trim()}
              >
                {rejecting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <XCircle className="mr-2 h-4 w-4" />
                )}
                Reject Analysis
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* LPO Generation Modal */}
        <LPOGenerationModal
          open={showLPOModal}
          onClose={() => setShowLPOModal(false)}
          bidAnalysis={bidAnalysis}
        />
      </div>
    </Screen>
  );
};

export default BidAnalysisDetail;
