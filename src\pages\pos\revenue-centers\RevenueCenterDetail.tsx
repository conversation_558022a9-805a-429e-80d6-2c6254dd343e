import React, { useState } from 'react';
import { useNavigate, usePara<PERSON>, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Edit, 
  Store, 
  Building2, 
  Calculator,
  Monitor,
  MoreHorizontal,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { RevenueCenter } from '@/types/pos';

// Mock data
const mockRevenueCenter: RevenueCenter = {
  id: 'rc1',
  code: 'RC001',
  name: 'Main Restaurant',
  description: 'Primary dining area with full service',
  branchId: '1',
  branch: { id: '1', code: 'BR001', name: 'Downtown Hotel', physicalLocation: 'Nairobi CBD', address: '', timezone: '', currency: '', taxConfiguration: [], isActive: true, createdAt: '', updatedAt: '' },
  taxClassId: 'tax-class-1',
  salesCategories: ['food', 'beverages', 'desserts'],
  isActive: true,
  serviceChargeApplies: true,
  serviceChargeSettings: { percentage: 10, isInclusive: false, applicableCategories: ['food'] },
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z'
};

const RevenueCenterDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [revenueCenter] = useState<RevenueCenter>(mockRevenueCenter);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate('/pos/revenue-centers')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <Store className="h-6 w-6" />
              <h1 className="text-3xl font-bold tracking-tight">{revenueCenter.name}</h1>
              <Badge variant={revenueCenter.isActive ? 'default' : 'secondary'}>
                {revenueCenter.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              Revenue Center Code: {revenueCenter.code} • {revenueCenter.branch?.name}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link to={`/pos/revenue-centers/${revenueCenter.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              Edit Revenue Center
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>
                <Monitor className="h-4 w-4 mr-2" />
                Manage Workstations
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Calculator className="h-4 w-4 mr-2" />
                Tax Configuration
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {revenueCenter.isActive ? 'Deactivate' : 'Activate'} Revenue Center
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workstations">Workstations</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Branch</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{revenueCenter.branch?.name}</div>
                    <div className="text-sm text-muted-foreground">{revenueCenter.branch?.code}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Tax Class</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Calculator className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{revenueCenter.taxClassId || 'Not assigned'}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Service Charge</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Badge variant={revenueCenter.serviceChargeApplies ? 'default' : 'secondary'}>
                    {revenueCenter.serviceChargeApplies ? 'Enabled' : 'Disabled'}
                  </Badge>
                  {revenueCenter.serviceChargeApplies && revenueCenter.serviceChargeSettings && (
                    <div className="text-sm text-muted-foreground mt-1">
                      {revenueCenter.serviceChargeSettings.percentage}% 
                      {revenueCenter.serviceChargeSettings.isInclusive ? ' (Inclusive)' : ' (Exclusive)'}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Sales Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{revenueCenter.salesCategories.length}</div>
                <p className="text-sm text-muted-foreground">categories</p>
              </CardContent>
            </Card>
          </div>

          {/* Description */}
          {revenueCenter.description && (
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{revenueCenter.description}</p>
              </CardContent>
            </Card>
          )}

          {/* Sales Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Sales Categories</CardTitle>
              <CardDescription>
                Types of products and services sold in this revenue center
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {revenueCenter.salesCategories.map((category, index) => (
                  <Badge key={index} variant="outline" className="capitalize">
                    {category.replace('-', ' ')}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Service Charge Details */}
          {revenueCenter.serviceChargeApplies && revenueCenter.serviceChargeSettings && (
            <Card>
              <CardHeader>
                <CardTitle>Service Charge Configuration</CardTitle>
                <CardDescription>
                  Service charge settings for this revenue center
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Percentage</h4>
                    <div className="text-2xl font-bold">{revenueCenter.serviceChargeSettings.percentage}%</div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Type</h4>
                    <Badge variant="outline">
                      {revenueCenter.serviceChargeSettings.isInclusive ? 'Inclusive' : 'Exclusive'}
                    </Badge>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Applicable Categories</h4>
                    <div className="space-y-1">
                      {revenueCenter.serviceChargeSettings.applicableCategories.map((category, index) => (
                        <Badge key={index} variant="secondary" className="mr-1">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="workstations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Assigned Workstations</CardTitle>
              <CardDescription>
                POS devices and terminals assigned to this revenue center
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Monitor className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Workstations Assigned</h3>
                <p className="text-muted-foreground mb-4">
                  This revenue center doesn't have any workstations assigned yet.
                </p>
                <Link to="/pos/workstations/new">
                  <Button>
                    <Monitor className="h-4 w-4 mr-2" />
                    Add Workstation
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Center Settings</CardTitle>
              <CardDescription>
                Configuration and operational settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">General Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Code:</span>
                      <span className="font-medium">{revenueCenter.code}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <Badge variant={revenueCenter.isActive ? 'default' : 'secondary'}>
                        {revenueCenter.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Created:</span>
                      <span className="font-medium">
                        {new Date(revenueCenter.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Last Updated:</span>
                      <span className="font-medium">
                        {new Date(revenueCenter.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Configuration</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Tax Class:</span>
                      <span className="font-medium">{revenueCenter.taxClassId || 'Not assigned'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Sales Categories:</span>
                      <span className="font-medium">{revenueCenter.salesCategories.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Service Charge:</span>
                      <Badge variant={revenueCenter.serviceChargeApplies ? 'default' : 'secondary'}>
                        {revenueCenter.serviceChargeApplies ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RevenueCenterDetail;
