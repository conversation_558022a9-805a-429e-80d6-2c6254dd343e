import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { ArrowLeft, Save, X, Calculator } from 'lucide-react';
import { TaxClassFormData } from '@/types/pos';

interface TaxClassFormProps {
  mode: 'create' | 'edit';
}

// Mock data
const mockRevenueCenters = [
  { id: 'rc1', name: 'Main Restaurant', code: 'RC001' },
  { id: 'rc2', name: 'Bar & Lounge', code: 'RC002' },
  { id: 'rc3', name: 'Room Service', code: 'RC003' },
  { id: 'rc4', name: 'Catering', code: 'RC004' }
];

const TaxClassForm: React.FC<TaxClassFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<TaxClassFormData>({
    defaultValues: {
      name: '',
      description: '',
      applicableRevenueCenters: [],
    },
  });

  const onSubmit = async (data: TaxClassFormData) => {
    setIsLoading(true);
    try {
      console.log('Submitting tax class data:', data);
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigate('/pos/tax-configuration');
    } catch (error) {
      console.error('Error saving tax class:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => navigate('/pos/tax-configuration')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === 'create' ? 'Add Tax Class' : 'Edit Tax Class'}
          </h1>
          <p className="text-muted-foreground">
            {mode === 'create' 
              ? 'Create a new tax class for categorizing tax rates'
              : 'Update tax class information and settings'
            }
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>Tax Class Information</span>
              </CardTitle>
              <CardDescription>
                Enter the basic details for the tax class
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                rules={{ required: 'Tax class name is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tax Class Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Food Tax, Beverage Tax, Service Tax" {...field} />
                    </FormControl>
                    <FormDescription>
                      A descriptive name for this tax class
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                rules={{ required: 'Description is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description *</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe what this tax class covers and when it applies"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Detailed description of what this tax class covers
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Revenue Center Assignment */}
          <Card>
            <CardHeader>
              <CardTitle>Revenue Center Assignment</CardTitle>
              <CardDescription>
                Select which revenue centers this tax class applies to
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="applicableRevenueCenters"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Applicable Revenue Centers</FormLabel>
                    <FormDescription>
                      Select revenue centers where this tax class will be available
                    </FormDescription>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      {mockRevenueCenters.map((rc) => (
                        <div key={rc.id} className="flex items-center space-x-2 p-3 border rounded-lg">
                          <Checkbox
                            id={rc.id}
                            checked={field.value.includes(rc.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                field.onChange([...field.value, rc.id]);
                              } else {
                                field.onChange(field.value.filter(id => id !== rc.id));
                              }
                            }}
                          />
                          <div className="flex-1">
                            <Label htmlFor={rc.id} className="font-medium">
                              {rc.name}
                            </Label>
                            <div className="text-sm text-muted-foreground">
                              {rc.code}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-800">
                        <strong>Note:</strong> If no revenue centers are selected, this tax class will be available to all revenue centers by default.
                      </p>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Usage Information */}
          {mode === 'edit' && (
            <Card>
              <CardHeader>
                <CardTitle>Usage Information</CardTitle>
                <CardDescription>
                  Current usage of this tax class across the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">3</div>
                    <div className="text-sm text-muted-foreground">Tax Rates</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">25</div>
                    <div className="text-sm text-muted-foreground">Menu Items</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">2</div>
                    <div className="text-sm text-muted-foreground">Revenue Centers</div>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <p className="text-sm text-amber-800">
                    <strong>Warning:</strong> This tax class is currently in use. Changes may affect existing menu items and transactions.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => navigate('/pos/tax-configuration')}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create Tax Class' : 'Update Tax Class'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default TaxClassForm;
