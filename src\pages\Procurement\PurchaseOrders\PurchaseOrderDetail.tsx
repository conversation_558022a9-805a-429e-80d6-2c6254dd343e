import { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Screen } from "@/app-components/layout/screen";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import SendEmailModal from "./modals/SendEmailModal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  useGetPurchaseOrderQuery,
  useSubmitPurchaseOrderMutation,
  useApprovePurchaseOrderMutation,
  useRejectPurchaseOrderMutation,
  useSendPurchaseOrderEmailMutation,
  useExportPurchaseOrderPDFMutation,
} from "@/redux/slices/procurement";
import { 
  ArrowLeft, 
  Send, 
  CheckCircle, 
  XCircle, 
  FileText, 
  Calendar,
  User,
  Building,
  Package,
  Loader2,
  Mail,
  Download,
  MapPin,
  CreditCard,
  DollarSign
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import { downloadPurchaseOrderPDF, printPurchaseOrder } from "@/utils/purchaseOrderPDF";

const PurchaseOrderDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [rejectReason, setRejectReason] = useState("");

  const { data: purchaseOrder, isLoading, error } = useGetPurchaseOrderQuery(id!);
  
  const [submitPurchaseOrder, { isLoading: submitting }] = useSubmitPurchaseOrderMutation();
  const [approvePurchaseOrder, { isLoading: approving }] = useApprovePurchaseOrderMutation();
  const [rejectPurchaseOrder, { isLoading: rejecting }] = useRejectPurchaseOrderMutation();
  const [sendPurchaseOrderEmail, { isLoading: sending }] = useSendPurchaseOrderEmailMutation();
  const [exportPurchaseOrderPDF, { isLoading: exporting }] = useExportPurchaseOrderPDFMutation();

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
      "Pending Approval": { variant: "default" as const, color: "bg-yellow-100 text-yellow-800" },
      Approved: { variant: "default" as const, color: "bg-green-100 text-green-800" },
      Sent: { variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      Cancelled: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };

  const handleSubmit = async () => {
    try {
      await submitPurchaseOrder(Number(id)).unwrap();
      toast.success("Purchase order submitted for approval");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit purchase order");
    }
  };

  const handleApprove = async () => {
    try {
      await approvePurchaseOrder(Number(id)).unwrap();
      toast.success("Purchase order approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve purchase order");
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }
    
    try {
      await rejectPurchaseOrder({ id: Number(id), reason: rejectReason }).unwrap();
      toast.success("Purchase order rejected");
      setShowRejectDialog(false);
      setRejectReason("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to reject purchase order");
    }
  };

  const handleSendEmail = async (emailData: any) => {
    try {
      await sendPurchaseOrderEmail({ id: Number(id), email_data: emailData }).unwrap();
      toast.success("Purchase order sent to supplier");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to send purchase order");
      throw error;
    }
  };

  const handleExportPDF = async () => {
    if (!purchaseOrder) return;

    try {
      // Try API export first
      const blob = await exportPurchaseOrderPDF(Number(id)).unwrap();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `purchase-order-${purchaseOrder.po_number}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Purchase order exported successfully");
    } catch (error: any) {
      // Fallback to client-side PDF generation
      try {
        downloadPurchaseOrderPDF(purchaseOrder);
        toast.success("Purchase order downloaded successfully");
      } catch (fallbackError) {
        toast.error("Failed to export purchase order");
      }
    }
  };

  const handlePrint = () => {
    if (!purchaseOrder) return;
    printPurchaseOrder(purchaseOrder);
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Screen>
    );
  }

  if (error || !purchaseOrder) {
    return (
      <Screen>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Purchase Order Not Found</h2>
          <p className="text-gray-600 mb-4">The purchase order you're looking for doesn't exist or has been removed.</p>
          <Button onClick={() => navigate("/procurement/purchase-orders")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Purchase Orders
          </Button>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/procurement/purchase-orders")}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                Purchase Order {purchaseOrder.po_number}
              </h1>
              <div className="flex items-center gap-2 mt-1">
                {getStatusBadge(purchaseOrder.status || "Draft")}
                <span className="text-gray-500">•</span>
                <span className="text-gray-600">
                  Created {new Date(purchaseOrder.created_at || "").toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handlePrint}
            >
              <FileText className="mr-2 h-4 w-4" />
              Print
            </Button>

            <Button
              variant="outline"
              onClick={handleExportPDF}
              disabled={exporting}
            >
              {exporting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Download className="mr-2 h-4 w-4" />
              )}
              Export PDF
            </Button>

            {purchaseOrder.status === "Draft" && (
              <Button onClick={handleSubmit} disabled={submitting}>
                {submitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                Submit for Approval
              </Button>
            )}

            {purchaseOrder.status === "Pending Approval" && (
              <>
                <Button onClick={handleApprove} disabled={approving}>
                  {approving ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle className="mr-2 h-4 w-4" />
                  )}
                  Approve
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowRejectDialog(true)}
                  disabled={rejecting}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </Button>
              </>
            )}

            {purchaseOrder.status === "Approved" && (
              <Button onClick={() => setShowEmailModal(true)} disabled={sending}>
                {sending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Mail className="mr-2 h-4 w-4" />
                )}
                Send to Supplier
              </Button>
            )}
          </div>
        </div>

        {/* Purchase Order Details */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Purchase Order Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">PO Number</Label>
                    <p className="font-medium">{purchaseOrder.po_number}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Status</Label>
                    <div className="mt-1">
                      {getStatusBadge(purchaseOrder.status || "Draft")}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Total Value</Label>
                    <p className="font-medium text-lg">
                      {purchaseOrder.currency} {purchaseOrder.total_value?.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Currency</Label>
                    <p className="font-medium">{purchaseOrder.currency}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Items Table */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Order Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead className="text-right">Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {purchaseOrder.items?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.product_name}</div>
                          </div>
                        </TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.unit_of_measure_name}</TableCell>
                        <TableCell>
                          {purchaseOrder.currency} {item.unit_price?.toLocaleString()}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          {purchaseOrder.currency} {((item.quantity || 0) * (item.unit_price || 0)).toLocaleString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Order Summary */}
                <Separator className="my-4" />
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span className="font-medium">
                      {purchaseOrder.currency} {purchaseOrder.subtotal?.toLocaleString()}
                    </span>
                  </div>
                  {purchaseOrder.tax_amount && (
                    <div className="flex justify-between">
                      <span>Tax ({purchaseOrder.tax_rate}%):</span>
                      <span className="font-medium">
                        {purchaseOrder.currency} {purchaseOrder.tax_amount?.toLocaleString()}
                      </span>
                    </div>
                  )}
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span>
                      {purchaseOrder.currency} {purchaseOrder.total_value?.toLocaleString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Supplier Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Supplier Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Supplier Name</Label>
                  <p className="font-medium">{purchaseOrder.supplier_name}</p>
                </div>
                {purchaseOrder.supplier_email && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Email</Label>
                    <p className="text-sm">{purchaseOrder.supplier_email}</p>
                  </div>
                )}
                {purchaseOrder.supplier_phone && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Phone</Label>
                    <p className="text-sm">{purchaseOrder.supplier_phone}</p>
                  </div>
                )}
                {purchaseOrder.supplier_address && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Address</Label>
                    <p className="text-sm">{purchaseOrder.supplier_address}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Delivery Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Delivery Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Delivery Location</Label>
                  <p className="font-medium">{purchaseOrder.delivery_location_name}</p>
                </div>
                {purchaseOrder.delivery_address && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Delivery Address</Label>
                    <p className="text-sm">{purchaseOrder.delivery_address}</p>
                  </div>
                )}
                <div>
                  <Label className="text-sm font-medium text-gray-500">Expected Delivery Date</Label>
                  <p className="font-medium">
                    {purchaseOrder.delivery_date
                      ? new Date(purchaseOrder.delivery_date).toLocaleDateString()
                      : "Not specified"
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Terms
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Payment Terms</Label>
                  <p className="font-medium">{purchaseOrder.payment_terms || "Not specified"}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Currency</Label>
                  <p className="font-medium">{purchaseOrder.currency}</p>
                </div>
                {purchaseOrder.requires_director_approval && (
                  <div className="p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <p className="text-sm text-yellow-800 font-medium">
                      Requires Director Approval
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Creation Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Creation Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Created By</Label>
                  <p className="font-medium">{purchaseOrder.created_by_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Created Date</Label>
                  <p className="text-sm">
                    {new Date(purchaseOrder.created_at || "").toLocaleString()}
                  </p>
                </div>
                {purchaseOrder.approved_by_name && (
                  <>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Approved By</Label>
                      <p className="font-medium">{purchaseOrder.approved_by_name}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Approved Date</Label>
                      <p className="text-sm">
                        {new Date(purchaseOrder.approved_at || "").toLocaleString()}
                      </p>
                    </div>
                  </>
                )}
                {purchaseOrder.sent_at && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Sent to Supplier</Label>
                    <p className="text-sm">
                      {new Date(purchaseOrder.sent_at).toLocaleString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Notes and Terms */}
        {(purchaseOrder.notes || purchaseOrder.terms_and_conditions) && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {purchaseOrder.notes && (
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm whitespace-pre-wrap">{purchaseOrder.notes}</p>
                </CardContent>
              </Card>
            )}

            {purchaseOrder.terms_and_conditions && (
              <Card>
                <CardHeader>
                  <CardTitle>Terms and Conditions</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm whitespace-pre-wrap">{purchaseOrder.terms_and_conditions}</p>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Reject Dialog */}
        <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reject Purchase Order</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="reject-reason">Reason for Rejection</Label>
                <Textarea
                  id="reject-reason"
                  placeholder="Please provide a reason for rejecting this purchase order..."
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowRejectDialog(false);
                  setRejectReason("");
                }}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleReject}
                disabled={rejecting || !rejectReason.trim()}
              >
                {rejecting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <XCircle className="mr-2 h-4 w-4" />
                )}
                Reject Purchase Order
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Send Email Modal */}
        <SendEmailModal
          open={showEmailModal}
          onClose={() => setShowEmailModal(false)}
          purchaseOrder={purchaseOrder}
          onSend={handleSendEmail}
          isLoading={sending}
        />
      </div>
    </Screen>
  );
};

export default PurchaseOrderDetail;
