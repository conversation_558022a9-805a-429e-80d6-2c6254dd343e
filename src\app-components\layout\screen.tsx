import * as React from "react";
import { AppSidebar } from "@/app-components/sidebar/app-sidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { useTheme } from "@/hooks/use-theme";
// import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Moon, Sun, LogOut, Calendar, Bell, Settings, UserRound } from "lucide-react";
import { useAuthHook } from "@/utils/useAuthHook";
import { toast } from "@/components/custom/Toast/MyToast";
import { DestructiveButton } from "@/components/custom/buttons/buttons";
import useCurrentCurrencyStore from "@/zustand/useCurrentCurrencyStore";
//import Footer from "@/components/custom/footer/Footer";

interface ScreenProps {
  children: React.ReactNode;
  headerContent?: React.ReactNode;
}

export function Screen({ children, headerContent }: ScreenProps) {
  const { toggleTheme, theme } = useTheme();

  

  // auth
  const { isAuthenticated, user_details, token } = useAuthHook();
  
  return (
    <SidebarProvider>
      <div className="flex h-screen w-screen overflow-hidden">
        <AppSidebar />

        <SidebarInset className="flex flex-col flex-1 w-full overflow-hidden">
          <header
            className="
            flex h-16 shrink-0 items-center justify-between
            px-4 bg-white text-black shadow-md
            dark:bg-gray-900 dark:text-gray-100
            transition-all
            group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 border-b sticky top-0 z-10
          "
          >
            <div className="flex items-center space-x-2">
              <SidebarTrigger className="dark:bg-gray-700 dark:text-white" />

              <Separator orientation="vertical" className="h-6" />

              <div>{headerContent}</div>
            </div>

            <div className="flex items-center space-x-6">
              {/* NOTIFICATION BELL */}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    className="p-2 rounded-full relative hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Notifications"
                  >
                    <Bell className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                    <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-64 rounded-lg bg-white text-black border dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                  align="end"
                  sideOffset={6}
                >
                  <DropdownMenuLabel className="py-2">
                    Notifications
                  </DropdownMenuLabel>
                  
                </DropdownMenuContent>
              </DropdownMenu>

              

              {/* SETTINGS DROPDOWN */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Settings"
                  >
                    <Settings className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-48 rounded-lg bg-white text-black border dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                  align="end"
                  sideOffset={6}
                >
                  <DropdownMenuLabel className="py-2">
                    Settings
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={toggleTheme}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    {theme === "dark" ? (
                      <>
                        <Sun className="w-4 h-4" />
                        Switch to Light Mode
                      </>
                    ) : (
                      <>
                        <Moon className="w-4 h-4" />
                        Switch to Dark Mode
                      </>
                    )}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              {/* USER AVATAR + DROPDOWN */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    className="
                    flex items-center gap-2
                    rounded-full p-0
                    transition-colors
                  "
                  >
                    <Avatar className="h-10 w-10 rounded-full flex justify-center items-center border border-foreground">
                      <UserRound className='' size={23} />
                      
                    </Avatar>
                    <div className="hidden sm:grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold text-xs">{user_details?.fullnames}</span>
                      <span className="truncate text-[10px]">{user_details?.email}</span>
                    </div>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-56 rounded-lg bg-white text-black border dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                  align="end"
                  sideOffset={6}
                >
                  <DropdownMenuLabel className="py-3">
                    My Account
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="truncate max-w-[200px] hover:!bg-transparent hover:!text-foreground">
                    {user_details?.fullnames}
                  </DropdownMenuItem>
                  <DropdownMenuItem className="truncate max-w-[200px] hover:!bg-transparent hover:!text-foreground">
                    {user_details?.email}
                  </DropdownMenuItem>
                  <DropdownMenuItem className="truncate max-w-[200px] hover:!bg-transparent hover:!text-foreground">
                    {user_details?.department} Dept.
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="dark:bg-gray-700" />
                  {isAuthenticated ? (
                    <DropdownMenuItem
                     
                    >
                      <DestructiveButton
                        size="sm"
                        className="w-full flex item-center gap-2"
                      >
                        <LogOut size={20} />
                        Log out
                      </DestructiveButton>
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem className="hover:!bg-none">
                      <DestructiveButton
                        size="sm"
                        className="w-full flex item-center gap-2"
                      >
                        <LogOut className="w-4 h-4" />
                        Log In
                      </DestructiveButton>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          <main
            className="
              flex-1 overflow-auto
              bg-white text-black dark:bg-gray-900 dark:text-gray-100
            "
          >
            <div className="h-full w-full p-4">{children}</div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
