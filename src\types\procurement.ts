// Store Requisition Types
export interface StoreRequisition {
  id?: number;
  requested_by?: number;
  requested_by_name?: string;
  cost_center?: number;
  cost_center_name?: string;
  store?: number;
  store_name?: string;
  status?: "Draft" | "Submitted" | "Approved" | "Rejected";
  purpose?: string;
  required_by?: string; // DateField as string
  created_at?: string;
  updated_at?: string;
  items?: StoreRequisitionItem[];
}

export interface StoreRequisitionItem {
  id?: number;
  requisition?: number;
  product?: number;
  product_name?: string;
  quantity?: number;
  unit_of_measure?: number;
  unit_of_measure_name?: string;
  remarks?: string;
}

// Purchase Requisition Types
export interface PurchaseRequisition {
  id?: number;
  pr_number?: string; // Auto-generated purchase requisition number
  store_requisition?: number;
  store_requisition_number?: string;
  created_by?: number;
  created_by_name?: string;
  assigned_to?: number; // Procurement Officer
  assigned_to_name?: string;
  cost_center?: number;
  cost_center_name?: string;
  department?: string;
  supplier_category?: string;
  budget_code?: string;
  total_estimated_cost?: number;
  currency?: string;
  required_date?: string;
  priority?: "Low" | "Medium" | "High" | "Urgent";
  status?: "Draft" | "Submitted" | "Approved" | "Rejected" | "Converted to RFQ";
  approval_notes?: string;
  created_at?: string;
  updated_at?: string;
  items?: PurchaseRequisitionItem[];
}

export interface PurchaseRequisitionItem {
  id?: number;
  requisition?: number;
  product?: number;
  product_name?: string;
  product_code?: string;
  product_category?: string;
  quantity?: number;
  unit_of_measure?: number;
  unit_of_measure_name?: string;
  estimated_unit_cost?: number;
  estimated_total_cost?: number;
  specifications?: string;
  preferred_supplier?: string;
  justification?: string;
}

// RFQ Types
export interface RFQ {
  id?: number;
  requisition?: number;
  rfq_number?: string;
  response_deadline?: string;
  created_by?: number;
  created_by_name?: string;
  status?: "Open" | "Closed" | "Cancelled";
  created_at?: string;
  items?: RFQItem[];
  responses?: RFQResponse[];
}

export interface RFQItem {
  id?: number;
  rfq?: number;
  product?: number;
  product_name?: string;
  quantity?: number;
  unit_of_measure?: number;
  unit_of_measure_name?: string;
}

export interface RFQResponse {
  id?: number;
  rfq?: number;
  supplier?: number;
  supplier_name?: string;
  submitted_at?: string;
  submitted_by?: string;
  notes?: string;
  items?: RFQResponseItem[];
}

export interface RFQResponseItem {
  id?: number;
  response?: number;
  product?: number;
  product_name?: string;
  unit_price?: number;
  delivery_time_days?: number;
  currency?: string;
}

// Bid Analysis Types
export interface BidAnalysis {
  id?: number;
  rfq?: number;
  created_by?: number;
  created_by_name?: string;
  split_award?: boolean;
  recommendation_notes?: string;
  selected_suppliers?: number[];
  selected_suppliers_names?: string[];
  finalized_at?: string;
}

// Purchase Order Types
export interface PurchaseOrder {
  id?: number;
  po_number?: string;
  supplier?: number;
  supplier_name?: string;
  supplier_email?: string;
  supplier_phone?: string;
  supplier_address?: string;
  status?: "Draft" | "Pending Approval" | "Approved" | "Sent" | "Cancelled";
  delivery_location?: number;
  delivery_location_name?: string;
  delivery_address?: string;
  payment_terms?: string;
  delivery_date?: string;
  currency?: string;
  total_value?: number;
  subtotal?: number;
  tax_amount?: number;
  tax_rate?: number;
  requires_director_approval?: boolean;
  created_by?: number;
  created_by_name?: string;
  approved_by?: number;
  approved_by_name?: string;
  approved_at?: string;
  sent_at?: string;
  notes?: string;
  terms_and_conditions?: string;
  created_at?: string;
  updated_at?: string;
  items?: PurchaseOrderItem[];
}

export interface PurchaseOrderItem {
  id?: number;
  purchase_order?: number;
  product?: number;
  product_name?: string;
  quantity?: number;
  unit_price?: number;
  unit_of_measure?: number;
  unit_of_measure_name?: string;
}

// GRN Types
export interface GRN {
  id?: number;
  grn_number?: string;
  purchase_order?: number;
  received_by?: number;
  received_by_name?: string;
  status?: "Partial" | "Full" | "Rejected";
  received_date?: string;
  store?: number;
  store_name?: string;
  items?: GRNItem[];
}

export interface GRNItem {
  id?: number;
  grn?: number;
  product?: number;
  product_name?: string;
  quantity_received?: number;
  unit_price?: number;
  unit_of_measure?: number;
  unit_of_measure_name?: string;
  expiry_date?: string;
}

// Supporting Types
export interface Product {
  id?: number;
  name?: string;
  code?: string;
  description?: string;
  category?: string;
  unit_of_measure?: number;
  unit_of_measure_name?: string;
}

export interface CostCenter {
  id?: number;
  name?: string;
  code?: string;
  department?: string;
}

export interface Store {
  id?: number;
  name?: string;
  location?: string;
  manager?: string;
}

export interface UnitOfMeasure {
  id?: number;
  name?: string;
  abbreviation?: string;
}

// API Response Types
export interface ProcurementApiResponse<T> {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: T[];
  };
}

// Form Types for UI
export interface StoreRequisitionFormData {
  cost_center: number | "";
  store: number | "";
  purpose: string;
  required_by: string;
  items: {
    product: number | "";
    quantity: number | "";
    unit_of_measure: number | "";
    remarks: string;
  }[];
}

export interface PurchaseRequisitionFormData {
  store_requisition: number | "";
  assigned_to: number | "";
  supplier_category: string;
  budget_code: string;
  required_date: string;
  priority: "Low" | "Medium" | "High" | "Urgent" | "";
  items: {
    product: number | "";
    quantity: number | "";
    unit_of_measure: number | "";
    estimated_unit_cost: number | "";
    specifications: string;
    preferred_supplier: string;
    justification: string;
  }[];
}

export interface PurchaseOrderFormData {
  supplier: number | "";
  delivery_location: number | "";
  delivery_address: string;
  payment_terms: string;
  delivery_date: string;
  currency: string;
  tax_rate: number | "";
  notes: string;
  terms_and_conditions: string;
  items: {
    product: number | "";
    quantity: number | "";
    unit_of_measure: number | "";
    unit_price: number | "";
  }[];
}

// Filter Types
export interface StoreRequisitionFilters {
  status?: string;
  cost_center?: number;
  store?: number;
  requested_by?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface PurchaseRequisitionFilters {
  status?: string;
  department?: string;
  supplier_category?: string;
  assigned_to?: number;
  priority?: string;
  cost_center?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface PurchaseOrderFilters {
  status?: string;
  supplier?: number;
  delivery_location?: number;
  currency?: string;
  requires_director_approval?: boolean;
  created_by?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface RFQFormData {
  requisition: number | string;
  suppliers: number[];
  delivery_location: number | "";
  delivery_address: string;
  required_date: string;
  response_deadline: string;
  notes: string;
  terms_and_conditions: string;
  items: {
    product: number | "";
    quantity: number | "";
    unit_of_measure: number | "";
    specifications: string;
    estimated_unit_cost: number | "";
  }[];
}

export interface RFQFilters {
  status?: string;
  supplier?: number;
  delivery_location?: number;
  category?: string;
  created_by?: number;
  date_from?: string;
  date_to?: string;
  response_deadline_from?: string;
  response_deadline_to?: string;
  search?: string;
}

export interface RFQResponseFormData {
  rfq: number;
  supplier: number;
  payment_terms: string;
  delivery_time_days: number | "";
  currency: string;
  notes: string;
  document?: File;
  items: {
    product: number;
    unit_price: number | "";
    delivery_time_days: number | "";
    notes: string;
  }[];
}

// Supporting Types for Purchase Requisitions
export interface ProcurementOfficer {
  id?: number;
  name?: string;
  email?: string;
  department?: string;
  employee_no?: string;
}

export interface Department {
  id?: number;
  name?: string;
  code?: string;
  manager?: string;
}

export interface SupplierCategory {
  id?: number;
  name?: string;
  description?: string;
}

export interface Supplier {
  id?: number;
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  contact_person?: string;
  tax_number?: string;
  registration_number?: string;
  category?: string;
  status?: "Active" | "Inactive" | "Suspended";
  payment_terms?: string;
  credit_limit?: number;
  currency?: string;
}

export interface PaymentTerm {
  id?: number;
  name?: string;
  description?: string;
  days?: number;
}

export interface Currency {
  id?: number;
  code?: string;
  name?: string;
  symbol?: string;
  exchange_rate?: number;
}

// RFQ Types
export interface RFQ {
  id?: number;
  rfq_number?: string;
  requisition?: number;
  requisition_number?: string;
  response_deadline?: string;
  created_by?: number;
  created_by_name?: string;
  status?: "Open" | "Closed" | "Cancelled";
  delivery_location?: number;
  delivery_location_name?: string;
  delivery_address?: string;
  required_date?: string;
  notes?: string;
  terms_and_conditions?: string;
  created_at?: string;
  updated_at?: string;
  items?: RFQItem[];
  suppliers?: number[];
  supplier_names?: string[];
  responses?: RFQResponse[];
  response_count?: number;
}

export interface RFQItem {
  id?: number;
  rfq?: number;
  product?: number;
  product_name?: string;
  product_code?: string;
  quantity?: number;
  unit_of_measure?: number;
  unit_of_measure_name?: string;
  specifications?: string;
  estimated_unit_cost?: number;
}

export interface RFQResponse {
  id?: number;
  rfq?: number;
  rfq_number?: string;
  supplier?: number;
  supplier_name?: string;
  supplier_email?: string;
  submitted_at?: string;
  submitted_by?: string;
  status?: "Received" | "Pending" | "Disqualified";
  payment_terms?: string;
  delivery_time_days?: number;
  currency?: string;
  notes?: string;
  is_complete?: boolean;
  has_discrepancies?: boolean;
  discrepancy_notes?: string;
  total_value?: number;
  document_url?: string;
  document_name?: string;
  items?: RFQResponseItem[];
}

export interface RFQResponseItem {
  id?: number;
  response?: number;
  product?: number;
  product_name?: string;
  unit_price?: number;
  total_price?: number;
  delivery_time_days?: number;
  currency?: string;
  notes?: string;
}

// Bid Analysis Types
export interface BidAnalysis {
  id?: number;
  rfq: number;
  rfq_number?: string;
  rfq_title?: string;
  analysis_number: string;
  status: "Draft" | "Under Review" | "Approved" | "Rejected" | "Converted";
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  created_by_name?: string;
  approved_by?: number;
  approved_by_name?: string;
  approved_at?: string;
  rejection_reason?: string;
  total_estimated_value?: number;
  total_selected_value?: number;
  currency?: string;
  notes?: string;
  audit_trail?: BidAnalysisAuditEntry[];
  items?: BidAnalysisItem[];
  supplier_responses?: RFQResponse[];
  conversion_data?: {
    purchase_orders_created?: number[];
    converted_at?: string;
    converted_by?: number;
    converted_by_name?: string;
  };
}

export interface BidAnalysisItem {
  id?: number;
  bid_analysis: number;
  rfq_item: number;
  product: number;
  product_name?: string;
  product_code?: string;
  quantity: number;
  unit_of_measure?: number;
  unit_of_measure_name?: string;
  specifications?: string;
  estimated_unit_cost?: number;
  selected_supplier?: number;
  selected_supplier_name?: string;
  selected_unit_price?: number;
  selected_total_price?: number;
  selection_reason?: string;
  is_split_award?: boolean;
  split_awards?: BidAnalysisItemSplit[];
  supplier_quotes?: SupplierQuote[];
  lowest_price?: number;
  lowest_price_supplier?: string;
  fastest_delivery?: number;
  fastest_delivery_supplier?: string;
  created_at?: string;
  updated_at?: string;
}

export interface BidAnalysisItemSplit {
  id?: number;
  bid_analysis_item: number;
  supplier: number;
  supplier_name?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  percentage: number;
  reason?: string;
}

export interface SupplierQuote {
  supplier: number;
  supplier_name?: string;
  unit_price?: number;
  total_price?: number;
  delivery_time_days?: number;
  payment_terms?: string;
  notes?: string;
  is_available: boolean;
  response_id?: number;
}

export interface BidAnalysisAuditEntry {
  id?: number;
  bid_analysis: number;
  action: "Created" | "Updated" | "Supplier Selected" | "Approved" | "Rejected" | "Converted";
  description: string;
  user: number;
  user_name?: string;
  timestamp: string;
  details?: {
    item_id?: number;
    supplier_id?: number;
    old_value?: any;
    new_value?: any;
    reason?: string;
  };
}

export interface SupplierSelection {
  item_id: number;
  supplier_id: number;
  supplier_name?: string;
  unit_price: number;
  total_price: number;
  quantity: number;
  reason?: string;
  is_split?: boolean;
  split_percentage?: number;
}

export interface BulkSupplierSelection {
  supplier_id: number;
  supplier_name?: string;
  item_ids: number[];
  reason?: string;
  apply_to_all?: boolean;
}

// Bid Analysis Form Data
export interface BidAnalysisFormData {
  rfq: number;
  notes: string;
  items: BidAnalysisItemFormData[];
}

export interface BidAnalysisItemFormData {
  rfq_item: number;
  selected_supplier?: number;
  selected_unit_price?: number;
  selection_reason?: string;
  is_split_award?: boolean;
  split_awards?: BidAnalysisItemSplitFormData[];
}

export interface BidAnalysisItemSplitFormData {
  supplier: number;
  quantity: number;
  unit_price: number;
  percentage: number;
  reason?: string;
}

// Bid Analysis Filters
export interface BidAnalysisFilters {
  status?: string;
  rfq?: string;
  created_by?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

// LPO Generation Data
export interface LPOGenerationData {
  bid_analysis_id: number;
  group_by_supplier?: boolean;
  delivery_terms?: string;
  payment_terms?: string;
  notes?: string;
  generate_separate_pos?: boolean;
}
