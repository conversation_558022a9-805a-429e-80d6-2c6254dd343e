import { apiSlice } from "../apiSlice";

// Types for Workstation API
export interface Workstation {
  id?: number;
  Workstation_code: string;
  name: string;
  is_active?: boolean;
  role?: 'POS' | 'Order Only' | 'self Service' | 'Kitchen dislay' | 'Bar' | 'Tablet' | 'Mobile';
  ip_address?: string;
  hostname?: string;
  supports_magnetic_card?: boolean;
  supports_employee_login?: boolean;
  language?: string;
  branch: string;
  revenue_center?: string;
  linked_printer?: string;
}

export const workstationApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all workstations
    getWorkstations: builder.query<Workstation[], any>({
      query: (params) => ({
        url: "/setup/workstations",
        method: "GET",
        params: params,
      }),
      providesTags: ["Workstations"],
    }),

    // Get single workstation
    getWorkstation: builder.query<Workstation, string>({
      query: (id) => ({
        url: `/setup/workstations/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Workstations", id }],
    }),

    // Create workstation
    createWorkstation: builder.mutation<Workstation, Partial<Workstation>>({
      query: (payload) => ({
        url: "/setup/workstations",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Workstations"],
    }),

    // Update workstation
    updateWorkstation: builder.mutation<Workstation, { id: string; data: Partial<Workstation> }>({
      query: ({ id, data }) => ({
        url: `/setup/workstations/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Workstations", id }, "Workstations"],
    }),

    // Delete workstation
    deleteWorkstation: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/workstations/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Workstations"],
    }),
  }),
});

export const { 
  useGetWorkstationsQuery, 
  useGetWorkstationQuery,
  useCreateWorkstationMutation,
  useUpdateWorkstationMutation,
  useDeleteWorkstationMutation,
} = workstationApiSlice;
