import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, FileText, TrendingUp, AlertCircle } from "lucide-react";
import {
  useCreateBidAnalysisMutation,
  useGetRFQsQuery,
  useGetRFQResponsesQuery,
} from "@/redux/slices/procurement";
import { BidAnalysisFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddBidAnalysisProps {
  open: boolean;
  onClose: () => void;
}

const AddBidAnalysis = ({ open, onClose }: AddBidAnalysisProps) => {
  const [createBidAnalysis, { isLoading: creating }] = useCreateBidAnalysisMutation();
  
  // Fetch supporting data
  const { data: rfqs } = useGetRFQsQuery({ status: "Closed" });

  const [formData, setFormData] = useState<BidAnalysisFormData>({
    rfq: 0,
    notes: "",
    items: [],
  });

  const [selectedRFQ, setSelectedRFQ] = useState<any>(null);

  // Fetch RFQ responses when RFQ is selected
  const { data: rfqResponses } = useGetRFQResponsesQuery(
    { rfq: formData.rfq },
    { skip: !formData.rfq }
  );

  const handleInputChange = (field: keyof BidAnalysisFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleRFQChange = (rfqId: string) => {
    const rfqIdNum = parseInt(rfqId);
    const rfq = rfqs?.data?.results?.find((r: any) => r.id === rfqIdNum);
    
    setSelectedRFQ(rfq);
    handleInputChange("rfq", rfqIdNum);
    
    // Initialize items based on RFQ items
    if (rfq?.items) {
      const items = rfq.items.map((item: any) => ({
        rfq_item: item.id,
        selected_supplier: undefined,
        selected_unit_price: undefined,
        selection_reason: "",
        is_split_award: false,
        split_awards: [],
      }));
      handleInputChange("items", items);
    }
  };

  const resetForm = () => {
    setFormData({
      rfq: 0,
      notes: "",
      items: [],
    });
    setSelectedRFQ(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.rfq) {
      toast.error("Please select an RFQ");
      return;
    }

    if (!rfqResponses?.data?.results?.length) {
      toast.error("Selected RFQ has no responses to analyze");
      return;
    }

    try {
      await createBidAnalysis(formData).unwrap();
      toast.success("Bid analysis created successfully");
      resetForm();
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create bid analysis");
    }
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  const responseCount = rfqResponses?.data?.results?.length || 0;
  const supplierNames = rfqResponses?.data?.results?.map((r: any) => r.supplier_name) || [];

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Create Bid Analysis
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* RFQ Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="h-4 w-4" />
                Select RFQ for Analysis
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="rfq">RFQ *</Label>
                <Select
                  value={formData.rfq ? formData.rfq.toString() : ""}
                  onValueChange={handleRFQChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select RFQ to analyze" />
                  </SelectTrigger>
                  <SelectContent>
                    {rfqs?.data?.results?.map((rfq: any) => (
                      <SelectItem key={rfq.id} value={rfq.id.toString()}>
                        <div className="flex flex-col">
                          <span className="font-medium">{rfq.rfq_number}</span>
                          <span className="text-sm text-gray-500">
                            {rfq.response_count || 0} response(s) • Created {new Date(rfq.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500 mt-1">
                  Only closed RFQs with responses are available for analysis
                </p>
              </div>

              {/* RFQ Summary */}
              {selectedRFQ && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Selected RFQ Summary</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-blue-700 font-medium">RFQ Number:</span>
                      <div>{selectedRFQ.rfq_number}</div>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Items:</span>
                      <div>{selectedRFQ.items?.length || 0} item(s)</div>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Delivery Location:</span>
                      <div>{selectedRFQ.delivery_location_name}</div>
                    </div>
                    <div>
                      <span className="text-blue-700 font-medium">Required Date:</span>
                      <div>
                        {selectedRFQ.required_date 
                          ? new Date(selectedRFQ.required_date).toLocaleDateString()
                          : "Not specified"
                        }
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Response Summary */}
          {formData.rfq && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Response Summary</CardTitle>
              </CardHeader>
              <CardContent>
                {responseCount > 0 ? (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <div className="text-2xl font-bold text-green-600">{responseCount}</div>
                      <div className="text-gray-600">supplier response(s) received</div>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium">Responding Suppliers:</Label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {supplierNames.map((name, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm"
                          >
                            {name}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="p-3 bg-green-50 border border-green-200 rounded">
                      <p className="text-green-800 text-sm">
                        ✓ This RFQ is ready for bid analysis. You can proceed to compare supplier responses and select winners.
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <p className="text-yellow-800 text-sm">
                      No responses found for this RFQ. Please select an RFQ with supplier responses.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Analysis Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="notes">Initial Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Add any initial notes or considerations for this bid analysis..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={creating || !formData.rfq || responseCount === 0}
            >
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Analysis"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddBidAnalysis;
