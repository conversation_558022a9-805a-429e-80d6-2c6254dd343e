import { Screen } from "@/app-components/layout/screen";
import AddStoreRequisition from "./modals/AddStoreRequisition";
import { useState } from "react";
import {
  useGetStoreRequisitionsQuery,
  useSubmitStoreRequisitionMutation,
  useApproveStoreRequisitionMutation,
  useRejectStoreRequisitionMutation,
  useDeleteStoreRequisitionMutation,
  useConvertToPurchaseRequisitionMutation
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { StoreRequisition } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { searchDebouncer } from "@/utils/debouncers";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Edit, Trash2, Send, CheckCircle, XCircle } from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const StoreRequisitionsIndex = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [statusFilter, setStatusFilter] = useState("");

  const {
    data: storeRequisitions,
  } = useGetStoreRequisitionsQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
    status: statusFilter,
  });

  // Mutation hooks
  const [submitStoreRequisition] = useSubmitStoreRequisitionMutation();
  const [approveStoreRequisition] = useApproveStoreRequisitionMutation();
  const [rejectStoreRequisition] = useRejectStoreRequisitionMutation();
  const [deleteStoreRequisition] = useDeleteStoreRequisitionMutation();
  const [convertToPurchaseRequisition] = useConvertToPurchaseRequisitionMutation();

  // Handler functions
  const handleSubmit = async (id: number) => {
    try {
      await submitStoreRequisition(id).unwrap();
      toast.success("Store requisition submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit requisition");
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await approveStoreRequisition(id).unwrap();
      toast.success("Store requisition approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve requisition");
    }
  };

  const handleReject = async (id: number) => {
    const reason = prompt("Please provide a reason for rejection:");
    if (!reason) return;

    try {
      await rejectStoreRequisition({ id, reason }).unwrap();
      toast.success("Store requisition rejected");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to reject requisition");
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm("Are you sure you want to delete this requisition?")) return;

    try {
      await deleteStoreRequisition(id).unwrap();
      toast.success("Store requisition deleted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to delete requisition");
    }
  };

  const handleConvertToPurchase = async (id: number) => {
    try {
      await convertToPurchaseRequisition(id).unwrap();
      toast.success("Successfully converted to purchase requisition");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to convert to purchase requisition");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
      Submitted: { variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      Approved: { variant: "default" as const, color: "bg-green-100 text-green-800" },
      Rejected: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };

  const columns: ColumnDef<StoreRequisition>[] = [
    {
      accessorKey: "id",
      header: "Req. No.",
      cell: (info) => (
        <Link
          to={`/procurement/store-requisitions/${info?.row?.original?.id}`}
          title="View Store Requisition"
        >
          <span className="font-medium underline capitalize text-blue-400">
            SR-{String(info.getValue()).padStart(4, '0')}
          </span>
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "purpose",
      header: "Purpose",
      cell: (info) => (
        <div className="max-w-xs truncate" title={info.getValue() as string}>
          {info.getValue() as string}
        </div>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "cost_center_name",
      header: "Cost Center",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "store_name",
      header: "Store",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "requested_by_name",
      header: "Requested By",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "required_by",
      header: "Required By",
      cell: (info) => {
        const date = info.getValue() as string;
        return date ? new Date(date).toLocaleDateString() : "N/A";
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => getStatusBadge(info.getValue() as string),
      enableColumnFilter: false,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: (info) => {
        const date = info.getValue() as string;
        return date ? new Date(date).toLocaleDateString() : "N/A";
      },
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const requisition = row.original;
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to={`/procurement/store-requisitions/${requisition.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              {requisition.status === "Draft" && (
                <>
                  <DropdownMenuItem>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleSubmit(requisition.id!)}>
                    <Send className="mr-2 h-4 w-4" />
                    Submit
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(requisition.id!)}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Submitted" && (
                <>
                  <DropdownMenuItem onClick={() => handleApprove(requisition.id!)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600" onClick={() => handleReject(requisition.id!)}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </DropdownMenuItem>
                </>
              )}
              {requisition.status === "Approved" && (
                <DropdownMenuItem onClick={() => handleConvertToPurchase(requisition.id!)}>
                  <Send className="mr-2 h-4 w-4" />
                  Convert to Purchase Req.
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold text-gray-800">Store Requisitions</h1>
        <div className="flex items-center gap-2">
          <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
            Create Requisition
          </Button>
        </div>
      </div>

      {/* Status Filter */}
      <div className="mb-4 flex gap-2">
        <Button
          variant={statusFilter === "" ? "default" : "outline"}
          size="sm"
          onClick={() => setStatusFilter("")}
        >
          All
        </Button>
        <Button
          variant={statusFilter === "Draft" ? "default" : "outline"}
          size="sm"
          onClick={() => setStatusFilter("Draft")}
        >
          Draft
        </Button>
        <Button
          variant={statusFilter === "Submitted" ? "default" : "outline"}
          size="sm"
          onClick={() => setStatusFilter("Submitted")}
        >
          Submitted
        </Button>
        <Button
          variant={statusFilter === "Approved" ? "default" : "outline"}
          size="sm"
          onClick={() => setStatusFilter("Approved")}
        >
          Approved
        </Button>
        <Button
          variant={statusFilter === "Rejected" ? "default" : "outline"}
          size="sm"
          onClick={() => setStatusFilter("Rejected")}
        >
          Rejected
        </Button>
      </div>

      <DataTable<StoreRequisition>
        data={storeRequisitions?.data?.results || []}
        columns={columns}
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enableColumnFilters={true}
        enableSorting={true}
        enablePrintPdf={true}
        tableClassName="border-collapse"
        tHeadClassName="bg-gray-50"
        tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
        tBodyTrClassName="hover:bg-gray-50"
        tBodyCellsClassName="border-t"
        searchInput={
          <input
            value={searchInput}
            name="searchInput"
            type="search"
            onChange={(e) =>
              searchDebouncer(e.target.value, setSearchInput, setSearchValue)
            }
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search requisitions..."
          />
        }
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        totalItems={storeRequisitions?.data?.total_data || 0}
      />

      {isAddModalOpen && (
        <AddStoreRequisition
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}
    </Screen>
  );
};

export default StoreRequisitionsIndex;
